import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Navbar from './Navbar';
import Footer from './Footer';

const DemoForm = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    company: '',
    date: '',
    time: '',
    message: ''
  });
  
  const [formSubmitted, setFormSubmitted] = useState(false);
  const [errors, setErrors] = useState({});

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData({
      ...formData,
      [name]: value
    });
    
    // Clear error when user starts typing
    if (errors[name]) {
      setErrors({
        ...errors,
        [name]: ''
      });
    }
  };

  const validateForm = () => {
    const newErrors = {};
    
    if (!formData.name.trim()) {
      newErrors.name = 'Name is required';
    }
    
    if (!formData.email.trim()) {
      newErrors.email = 'Email is required';
    } else if (!/\S+@\S+\.\S+/.test(formData.email)) {
      newErrors.email = 'Email is invalid';
    }
    
    if (!formData.date) {
      newErrors.date = 'Preferred date is required';
    }
    
    if (!formData.time) {
      newErrors.time = 'Preferred time is required';
    }
    
    return newErrors;
  };

  const handleSubmit = async () => {
    const newErrors = validateForm();
    
    if (Object.keys(newErrors).length > 0) {
      setErrors(newErrors);
      return;
    }
    
    try {
      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/email/demo`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(formData),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to submit demo request');
      }

      setFormSubmitted(true);
      
      // Reset form after submission
      setFormData({
        name: '',
        email: '',
        company: '',
        date: '',
        time: '',
        message: ''
      });
    } catch (error) {
      console.error('Demo form submission error:', error);
      setErrors({ submit: error.message || 'Failed to submit demo request. Please try again.' });
    }
  };

  return (
    <>
      <Navbar />
      <div className="pt-32 pb-20 bg-[#F9FAFB] min-h-screen">
        <div className="container mx-auto px-6">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="max-w-3xl mx-auto bg-white rounded-2xl shadow-lg p-8 md:p-12"
          >
            {!formSubmitted ? (
              <>
                <div className="text-center mb-10">
                  <h1 className="text-4xl font-serif text-[#1F2937] mb-4">Schedule a Demo</h1>
                  <p className="text-gray-600 font-sans max-w-xl mx-auto">
                    Experience the future of virtual try-on technology. Fill out the form below to schedule a personalized demo with our team.
                  </p>
                </div>
                
                <div className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="name" className="block text-sm font-medium text-gray-700 mb-1">
                        Name *
                      </label>
                      <input
                        type="text"
                        id="name"
                        name="name"
                        value={formData.name}
                        onChange={handleChange}
                        className={`w-full px-4 py-3 rounded-lg border ${
                          errors.name ? 'border-red-500' : 'border-gray-300'
                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}
                        placeholder="Your full name"
                        aria-required="true"
                        aria-invalid={errors.name ? 'true' : 'false'}
                        aria-describedby={errors.name ? 'name-error' : undefined}
                      />
                      {errors.name && (
                        <p id="name-error" className="mt-1 text-sm text-red-500">
                          {errors.name}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-1">
                        Email *
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={formData.email}
                        onChange={handleChange}
                        className={`w-full px-4 py-3 rounded-lg border ${
                          errors.email ? 'border-red-500' : 'border-gray-300'
                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}
                        placeholder="<EMAIL>"
                        aria-required="true"
                        aria-invalid={errors.email ? 'true' : 'false'}
                        aria-describedby={errors.email ? 'email-error' : undefined}
                      />
                      {errors.email && (
                        <p id="email-error" className="mt-1 text-sm text-red-500">
                          {errors.email}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="company" className="block text-sm font-medium text-gray-700 mb-1">
                      Company (Optional)
                    </label>
                    <input
                      type="text"
                      id="company"
                      name="company"
                      value={formData.company}
                      onChange={handleChange}
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      placeholder="Your company name"
                    />
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="date" className="block text-sm font-medium text-gray-700 mb-1">
                        Preferred Date *
                      </label>
                      <input
                        type="date"
                        id="date"
                        name="date"
                        value={formData.date}
                        onChange={handleChange}
                        className={`w-full px-4 py-3 rounded-lg border ${
                          errors.date ? 'border-red-500' : 'border-gray-300'
                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}
                        aria-required="true"
                        aria-invalid={errors.date ? 'true' : 'false'}
                        aria-describedby={errors.date ? 'date-error' : undefined}
                      />
                      {errors.date && (
                        <p id="date-error" className="mt-1 text-sm text-red-500">
                          {errors.date}
                        </p>
                      )}
                    </div>
                    
                    <div>
                      <label htmlFor="time" className="block text-sm font-medium text-gray-700 mb-1">
                        Preferred Time *
                      </label>
                      <input
                        type="time"
                        id="time"
                        name="time"
                        value={formData.time}
                        onChange={handleChange}
                        className={`w-full px-4 py-3 rounded-lg border ${
                          errors.time ? 'border-red-500' : 'border-gray-300'
                        } focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent`}
                        aria-required="true"
                        aria-invalid={errors.time ? 'true' : 'false'}
                        aria-describedby={errors.time ? 'time-error' : undefined}
                      />
                      {errors.time && (
                        <p id="time-error" className="mt-1 text-sm text-red-500">
                          {errors.time}
                        </p>
                      )}
                    </div>
                  </div>
                  
                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-gray-700 mb-1">
                      Message
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      rows="4"
                      className="w-full px-4 py-3 rounded-lg border border-gray-300 focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                      placeholder="Tell us about your specific interests or questions"
                    ></textarea>
                  </div>

                  {errors.submit && (
                    <div className="p-4 rounded-lg bg-red-50 text-red-500 text-sm">
                      {errors.submit}
                    </div>
                  )}

                  <div className="pt-4">
                    <button
                      type="button"
                      onClick={handleSubmit}
                      className="w-full bg-[#2D8C88] text-white px-6 py-4 rounded-full font-sans font-medium text-lg shadow-md hover:shadow-lg transition-all duration-200"
                      onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#F28C38')}
                      onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#2D8C88')}
                    >
                      Schedule My Demo
                    </button>
                  </div>
                </div>
              </>
            ) : (
              <motion.div
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                className="text-center py-12"
              >
                <div className="inline-flex items-center justify-center w-20 h-20 bg-green-100 rounded-full mb-6">
                  <svg 
                    xmlns="http://www.w3.org/2000/svg" 
                    className="h-10 w-10 text-green-600" 
                    fill="none" 
                    viewBox="0 0 24 24" 
                    stroke="currentColor"
                  >
                    <path 
                      strokeLinecap="round" 
                      strokeLinejoin="round" 
                      strokeWidth={2} 
                      d="M5 13l4 4L19 7" 
                    />
                  </svg>
                </div>
                <h2 className="text-3xl font-serif text-[#1F2937] mb-4">Thank You!</h2>
                <p className="text-gray-600 font-sans max-w-md mx-auto mb-8">
                  Your demo request has been submitted successfully. Our team will contact you shortly to confirm your appointment.
                </p>
                <button
                  onClick={() => setFormSubmitted(false)}
                  className="bg-[#2D8C88] text-white px-6 py-3 rounded-full font-sans font-medium shadow-md hover:shadow-lg transition-all duration-200"
                  onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#F28C38')}
                  onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#2D8C88')}
                >
                  Schedule Another Demo
                </button>
              </motion.div>
            )}
          </motion.div>
        </div>
      </div>
      <Footer />
    </>
  );
};

export default DemoForm;
