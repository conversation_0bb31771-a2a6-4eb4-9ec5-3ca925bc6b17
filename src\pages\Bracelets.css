/* Color Variables */
:root {
  --primary: #2D8C88;
  --secondary: #F28C38;
  --text-dark: #1F2937;
  --text-light: #6B7280;
  --background: #F9FAFB;
  --white: #FFFFFF;
}

/* Base Styles */
.bracelets-page {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  min-height: 100vh;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Button Styles */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 9999px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:hover {
  background-color: var(--secondary);
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-secondary:hover {
  background-color: var(--primary);
  color: var(--white);
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  padding: 6rem 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%);
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 9999px;
  margin-bottom: 1.5rem;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.75rem);
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: var(--text-light);
  margin-bottom: 2rem;
  max-width: 32rem;
}

.hero-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image {
  height: 40vh;
  object-fit: contain;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease-in-out;
}

.hero-image:hover {
  transform: scale(1.05);
}

.hero-image-shadow {
  position: absolute;
  bottom: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 75%;
  height: 1rem;
  border-radius: 9999px;
  background-color: rgba(31, 41, 55, 0.1);
  filter: blur(1rem);
}

/* Filter Section */
.filter-section {
  padding: 4rem 0;
  background-color: var(--white);
}

.filter-title {
  text-align: center;
  margin-bottom: 3rem;
}

.filter-buttons {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 2rem;
}

.filter-button {
  padding: 0.625rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 9999px;
  border: 1px solid #E5E7EB;
  background-color: var(--white);
  color: var(--text-dark);
  transition: all 0.2s ease-in-out;
  cursor: pointer;
}

.filter-button.active {
  background-color: var(--primary);
  color: var(--white);
  border-color: var(--primary);
}

.filter-button:hover:not(.active) {
  background-color: var(--secondary);
  color: var(--white);
  border-color: var(--secondary);
}

/* Product Grid */
.product-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(280px, 1fr));
  gap: 2rem;
  padding: 2rem 0;
}

.product-card {
  background-color: var(--white);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease-in-out;
}

.product-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.product-image-container {
  position: relative;
  aspect-ratio: 1;
  margin-bottom: 1rem;
  overflow: hidden;
  border-radius: 0.5rem;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: contain;
  transition: transform 0.3s ease-in-out;
}

.product-card:hover .product-image {
  transform: scale(1.05);
}

.product-name {
  font-size: 1.125rem;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.5rem;
}

.product-price {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--primary);
  margin-bottom: 1rem;
}

.product-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.try-on-link {
  display: flex;
  align-items: center;
  color: var(--primary);
  font-weight: 500;
  transition: color 0.2s ease-in-out;
}

.try-on-link:hover {
  color: var(--secondary);
}

.try-on-link svg {
  margin-right: 0.25rem;
  transition: transform 0.2s ease-in-out;
}

.try-on-link:hover svg {
  transform: scale(1.1);
}

.add-to-cart-button {
  padding: 0.5rem 1rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
}

.add-to-cart-button:not(.added) {
  background-color: var(--primary);
  color: var(--white);
}

.add-to-cart-button.added {
  background-color: var(--secondary);
  color: var(--white);
}

.add-to-cart-button:hover:not(.added) {
  background-color: var(--secondary);
}

/* Loading State */
.loading-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 4rem 0;
}

.loading-spinner {
  width: 3rem;
  height: 3rem;
  border: 3px solid rgba(45, 140, 136, 0.1);
  border-top-color: var(--primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Responsive Design */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }

  .hero-image {
    height: 50vh;
  }

  .product-grid {
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  }
}

@media (min-width: 768px) {
  .hero-section {
    padding: 8rem 0;
  }

  .hero-image {
    height: 60vh;
  }

  .filter-buttons {
    gap: 1rem;
  }

  .filter-button {
    padding: 0.75rem 2rem;
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }

  .hero-section {
    padding: 10rem 0;
  }

  .hero-image {
    height: 70vh;
  }

  .product-grid {
    grid-template-columns: repeat(3, 1fr);
  }
}

/* Touch Device Optimizations */
@media (hover: none) {
  .btn:hover,
  .filter-button:hover,
  .product-card:hover,
  .product-image:hover,
  .try-on-link:hover {
    transform: none;
  }

  .btn:active,
  .filter-button:active {
    transform: scale(0.98);
  }
}

/* Safari Mobile Fixes */
@supports (-webkit-touch-callout: none) {
  .hero-image {
    height: 40vh;
  }

  @media (min-width: 640px) {
    .hero-image {
      height: 50vh;
    }
  }

  @media (min-width: 768px) {
    .hero-image {
      height: 60vh;
    }
  }

  @media (min-width: 1024px) {
    .hero-image {
      height: 70vh;
    }
  }
}

/* Chrome Mobile Fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .hero-image {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
} 