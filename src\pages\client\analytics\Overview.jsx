import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  LineChart,
  Line
} from 'recharts';
import { Eye, Users, TrendingUp, Clock, User } from 'lucide-react';

const Overview = () => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [overviewData, setOverviewData] = useState(null);
  const [timeRange, setTimeRange] = useState('7d');

  useEffect(() => {
    const fetchOverviewData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;
        
        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch data from multiple endpoints
        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([
          fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }),
          fetch(`${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          }),
          fetch(`${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`, {
            headers: {
              'Authorization': `Bearer ${token}`,
              'Content-Type': 'application/json'
            }
          })
        ]);

        if (!timeAnalysisResponse.ok || !productPerformanceResponse.ok || !deviceStatsResponse.ok) {
          throw new Error('Failed to fetch analytics data');
        }

        const timeData = await timeAnalysisResponse.json();
        const productData = await productPerformanceResponse.json();
        const deviceData = await deviceStatsResponse.json();

        // Calculate total sessions and interactions from product data
        const totalSessions = productData.reduce((sum, product) => sum + (product.sessions || 0), 0);
        const totalInteractions = productData.reduce((sum, product) => sum + (product.totalInteractions || 0), 0);

        // Calculate average duration from time analysis data
        const avgDuration = timeData.dailyTrends?.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / 
          (timeData.dailyTrends?.length || 1);

        // Calculate unique users from device stats
        const uniqueUsers = deviceData.reduce((sum, device) => sum + (device.sessions || 0), 0);

        // Format the data for the overview
        const formattedData = {
          totalSessions,
          activeClients: uniqueUsers,
          avgSessionDuration: Math.round(avgDuration),
          totalInteractions,
          trends: timeData.dailyTrends?.map(trend => ({
            _id: trend._id,
            sessions: trend.sessions,
            avgDuration: Math.round(trend.avgDuration)
          })) || []
        };

        setOverviewData(formattedData);
      } catch (err) {
        console.error('Error fetching overview data:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchOverviewData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading overview</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Format duration from seconds to readable format
  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  // Format numbers with commas
  const formatNumber = (num) => {
    if (!num) return '0';
    return num.toLocaleString();
  };

  const metrics = [
    {
      title: 'Total Sessions',
      value: overviewData?.totalSessions,
      icon: <Users className="h-6 w-6 text-blue-500" />
    },
    {
      title: 'Avg. Duration',
      value: formatDuration(overviewData?.avgSessionDuration),
      icon: <Clock className="h-6 w-6 text-green-500" />
    },
    {
      title: 'Unique Users',
      value: overviewData?.activeClients,
      icon: <User className="h-6 w-6 text-purple-500" />
    }
  ];

  return (
    <div className="space-y-6">
      {/* Time Range Selector */}
      <div className="flex justify-end">
        <div className="inline-flex rounded-lg border border-gray-200 p-1">
          {['7d', '30d', '90d', '1y'].map((range) => (
            <button
              key={range}
              onClick={() => setTimeRange(range)}
              className={`px-3 py-1 text-sm font-medium rounded-md ${
                timeRange === range
                  ? 'bg-[#2D8C88] text-white'
                  : 'text-gray-600 hover:text-gray-900'
              }`}
            >
              {range}
            </button>
          ))}
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">
                  {formatNumber(metric.value)}
                </p>
              </div>
              <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                {metric.icon}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Session Trends - Line Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Trends</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={overviewData?.trends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="_id" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                  name="Total Sessions"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* Session Trends - Bar Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={overviewData?.trends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="_id" />
                <YAxis />
                <Tooltip />
                <Bar
                  dataKey="sessions"
                  fill="#2D8C88"
                  name="Total Sessions"
                />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default Overview; 