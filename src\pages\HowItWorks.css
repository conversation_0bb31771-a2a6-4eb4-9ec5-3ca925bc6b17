/* Color Variables */
:root {
  --primary: #2D8C88;
  --secondary: #F28C38;
  --text-dark: #1F2937;
  --text-light: #6B7280;
  --background: #F9FAFB;
  --white: #FFFFFF;
}

/* Base Styles */
.how-it-works-page {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  min-height: 100vh;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Button Styles */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 9999px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:hover {
  background-color: var(--secondary);
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-secondary:hover {
  background-color: var(--primary);
  color: var(--white);
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  padding: 6rem 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%);
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 9999px;
  margin-bottom: 1.5rem;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.75rem);
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: var(--text-light);
  margin-bottom: 2rem;
  max-width: 32rem;
}

.hero-image-container {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.hero-image {
  height: 40vh;
  object-fit: contain;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
  transition: transform 0.3s ease-in-out;
}

.hero-image:hover {
  transform: scale(1.05);
}

.hero-image-shadow {
  position: absolute;
  bottom: -2rem;
  left: 50%;
  transform: translateX(-50%);
  width: 75%;
  height: 1rem;
  border-radius: 9999px;
  background-color: rgba(31, 41, 55, 0.1);
  filter: blur(1rem);
}

/* Steps Section */
.steps-section {
  padding: 4rem 0;
  background-color: var(--white);
}

.step-card {
  background-color: var(--white);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: box-shadow 0.2s ease-in-out;
}

.step-card:hover {
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.step-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 9999px;
  background-color: rgba(45, 140, 136, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--primary);
}

.step-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
}

.step-description {
  font-size: 0.875rem;
  color: var(--text-dark);
  line-height: 1.5;
}

/* Video Section */
.video-section {
  padding: 4rem 0;
  background-color: var(--background);
}

.video-container {
  position: relative;
  aspect-ratio: 16/9;
  border-radius: 1rem;
  overflow: hidden;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.video-overlay {
  position: absolute;
  inset: 0;
  background-color: rgba(31, 41, 55, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
}

.play-button {
  width: 5rem;
  height: 5rem;
  border-radius: 9999px;
  background-color: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease-in-out;
}

.play-button:hover {
  background-color: rgba(255, 255, 255, 0.2);
}

.play-icon {
  width: 2.5rem;
  height: 2.5rem;
  color: var(--white);
  transition: transform 0.2s ease-in-out;
}

.play-button:hover .play-icon {
  transform: scale(1.1);
}

/* CTA Section */
.cta-section {
  padding: 5rem 0;
  background: linear-gradient(135deg, #1F2937 0%, #111827 100%);
}

.cta-title {
  font-size: clamp(2rem, 4vw, 2.5rem);
  color: var(--white);
  margin-bottom: 1.5rem;
}

.cta-description {
  font-size: 1.125rem;
  color: var(--text-light);
  margin-bottom: 3rem;
  max-width: 48rem;
}

/* Responsive Design */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }

  .hero-image {
    height: 50vh;
  }
}

@media (min-width: 768px) {
  .hero-section {
    padding: 8rem 0;
  }

  .hero-image {
    height: 60vh;
  }

  .step-card {
    padding: 2rem;
  }

  .step-title {
    font-size: 1.5rem;
  }

  .step-description {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }

  .hero-section {
    padding: 10rem 0;
  }

  .hero-image {
    height: 70vh;
  }
}

/* Touch Device Optimizations */
@media (hover: none) {
  .btn:hover,
  .step-card:hover,
  .play-button:hover {
    transform: none;
  }

  .btn:active,
  .play-button:active {
    transform: scale(0.98);
  }
}

/* Safari Mobile Fixes */
@supports (-webkit-touch-callout: none) {
  .hero-image {
    height: 40vh;
  }

  @media (min-width: 640px) {
    .hero-image {
      height: 50vh;
    }
  }

  @media (min-width: 768px) {
    .hero-image {
      height: 60vh;
    }
  }

  @media (min-width: 1024px) {
    .hero-image {
      height: 70vh;
    }
  }
}

/* Chrome Mobile Fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .hero-image {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
} 