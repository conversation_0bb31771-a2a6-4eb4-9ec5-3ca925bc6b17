import React, { useState } from 'react';
import { motion } from 'framer-motion';
import Overview from './Overview';
import DeviceStats from './DeviceStats';
import ProductPerformance from './ProductPerformance';
import TimeAnalysis from './TimeAnalysis';
import { 
  LayoutDashboard, 
  Smartphone, 
  Package, 
  Clock,
  BarChart3
} from 'lucide-react';

const ClientAnalytics = () => {
  const [activeTab, setActiveTab] = useState('overview');

  const tabs = [
    { id: 'overview', label: 'Overview', icon: LayoutDashboard },
    { id: 'devices', label: 'Device Stats', icon: Smartphone },
    { id: 'products', label: 'Product Performance', icon: Package },
    { id: 'time', label: 'Time Analysis', icon: Clock },
    { id: 'trends', label: 'Trends', icon: BarChart3 }
  ];

  const renderContent = () => {
    switch (activeTab) {
      case 'overview':
        return <Overview />;
      case 'devices':
        return <DeviceStats />;
      case 'products':
        return <ProductPerformance />;
      case 'time':
        return <TimeAnalysis />;
      case 'trends':
        return <Overview />; // Using Overview for trends as well
      default:
        return <Overview />;
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar */}
          <div className="w-full md:w-64 flex-shrink-0">
            <div className="bg-white rounded-xl shadow-sm p-4">
              <nav className="space-y-1">
                {tabs.map((tab) => {
                  const Icon = tab.icon;
                  return (
                    <motion.button
                      key={tab.id}
                      onClick={() => setActiveTab(tab.id)}
                      className={`w-full flex items-center px-4 py-3 text-sm font-medium rounded-lg transition-colors ${
                        activeTab === tab.id
                          ? 'bg-[#2D8C88] text-white'
                          : 'text-gray-600 hover:bg-gray-50'
                      }`}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Icon className="h-5 w-5 mr-3" />
                      {tab.label}
                    </motion.button>
                  );
                })}
              </nav>
            </div>
          </div>

          {/* Main Content */}
          <div className="flex-1">
            <motion.div
              key={activeTab}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              {renderContent()}
            </motion.div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClientAnalytics; 