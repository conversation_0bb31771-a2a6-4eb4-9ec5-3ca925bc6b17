# ViaTryon Client Integration Guide

## Overview
This guide explains how to integrate ViaTryon's virtual try-on functionality into your e-commerce website.

## Integration Steps

### 1. Get Your Client ID
- Log into your ViaTryon client dashboard
- Navigate to the "Code" section
- Your unique Client ID will be displayed (e.g., `507f1f77bcf86cd799439011`)

### 2. Generate Embed Code
1. In your client dashboard, click "Get Embed Code"
2. Configure the following:
   - **Product Image URL**: Direct URL to your product image with transparent background
   - **Product Type**: Choose between Watches or Bracelets
   - **Dimensions**: Watch case diameter or bracelet width in millimeters (e.g., 42 for watches, 15 for bracelets)
   - **Button Style**: Choose from Default, Primary, Outline, or Minimal
   - **Button Size**: Small, Medium, or Large

### 3. Add to Your Product Pages
Copy the generated embed code and paste it into your product page HTML where you want the "Try On Virtually" button to appear.

## Example Integration

```html
<!-- ViaTryon Virtual Try-On Button -->
<button
  onclick="openViaTryon('https://yoursite.com/images/watch.png', 'your_client_id', '42', 'watches')"
  class="viatryon-btn viatryon-btn-primary viatryon-btn-medium"
  style="
    background-color: #2D8C88;
    color: white;
    border: none;
    padding: 12px 24px;
    font-size: 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
  "
  onmouseover="this.style.opacity='0.8'"
  onmouseout="this.style.opacity='1'"
>
  Try On Virtually
</button>

<script>
function openViaTryon(imageUrl, clientId, caseDimensions, productType) {
  // Construct the ViaTryon URL with parameters
  const tryonUrl = 'https://viatryon.com/tryon?' +
    'image=' + encodeURIComponent(imageUrl) +
    '&client=' + encodeURIComponent(clientId) +
    '&size=' + encodeURIComponent(caseDimensions) +
    '&type=' + encodeURIComponent(productType || 'watches');

  // Open in new window/tab
  window.open(tryonUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');
}
</script>
```

## URL Parameters

The try-on page accepts the following URL parameters:

- `image`: Direct URL to the product image (required)
- `client`: Your unique client ID (automatically included in generated embed codes)
- `size`: Product dimensions in millimeters (optional, defaults to 42mm for watches, 15mm for bracelets)
- `type`: Product type - 'watches' or 'bracelets' (optional, defaults to 'watches')

## Image Requirements

For best results, product images should:
- Have a transparent background
- Be high resolution (minimum 500x500px)
- Show the product from a front-facing angle
- Be in PNG format for transparency support

## Testing Your Integration

1. Replace placeholder values with your actual data
2. Test the button on your staging environment
3. Verify the try-on experience opens correctly
4. Check that analytics are being tracked in your dashboard

## Support

For technical support or questions about integration:
- Check your client dashboard for analytics and troubleshooting
- Contact our support team through the dashboard
- Review the integration guide for common issues

## Analytics Tracking

All try-on sessions are automatically tracked with your client ID, providing insights into:
- Total try-on sessions
- Conversion rates
- User engagement metrics
- Device and geographic data

Access these analytics through your client dashboard.
