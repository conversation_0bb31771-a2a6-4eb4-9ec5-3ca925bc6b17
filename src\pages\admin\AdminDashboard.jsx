import React, { useState, useEffect } from 'react';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminNavbar from '../../components/admin/AdminNavbar';
import { motion } from 'framer-motion';
import { LineChart, Line, BarChart, Bar, XAxis, YAxis, CartesianGrid, Tooltip, ResponsiveContainer, PieChart, Pie, Cell } from 'recharts';
import { Users, Eye, Globe, Activity } from 'lucide-react';

const AdminDashboard = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [timeRange, setTimeRange] = useState('7d');
  const [dashboardData, setDashboardData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [recentActivity, setRecentActivity] = useState([]);
  const [deviceStats, setDeviceStats] = useState([]);
  const [topClients, setTopClients] = useState([]);
  const [uniqueUsers, setUniqueUsers] = useState(null);
  const [businessMetrics, setBusinessMetrics] = useState(null);

  // Fetch dashboard data from backend
  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch overview analytics
        const overviewResponse = await fetch(`${apiUrl}/api/analytics/admin/overview?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!overviewResponse.ok) {
          throw new Error('Failed to fetch overview data');
        }

        const overviewData = await overviewResponse.json();

        // Fetch recent activity
        const activityResponse = await fetch(`${apiUrl}/api/analytics/admin/recent-activity`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!activityResponse.ok) {
          throw new Error('Failed to fetch recent activity');
        }

        const activityData = await activityResponse.json();

        // Fetch unique users data
        const uniqueUsersResponse = await fetch(`${apiUrl}/api/analytics/admin/unique-users?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        let uniqueUsersData = null;
        if (uniqueUsersResponse.ok) {
          uniqueUsersData = await uniqueUsersResponse.json();
        }

        // Fetch device analytics
        const deviceResponse = await fetch(`${apiUrl}/api/analytics/admin/devices?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        let deviceAnalytics = null;
        if (deviceResponse.ok) {
          deviceAnalytics = await deviceResponse.json();
        }

        // Fetch client performance data
        const clientsResponse = await fetch(`${apiUrl}/api/analytics/admin/clients?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        let clientsData = null;
        if (clientsResponse.ok) {
          clientsData = await clientsResponse.json();
        }

        // Set all the data
        setDashboardData({
          totalTryOns: overviewData.totalSessions || 0,
          totalClients: overviewData.totalClients || 0,
          activeClients: overviewData.activeClients || 0,
          activeUsers: uniqueUsersData?.summary?.totalUniqueUsers || 0,
          trends: overviewData.trends || [],
          tryOnsGrowth: overviewData.tryOnsGrowth || 0,
          clientsGrowth: overviewData.clientsGrowth || 0,
          usersGrowth: overviewData.usersGrowth || 0
        });

        // Transform device data from backend - use device analytics if available, otherwise fallback to overview
        const deviceData = deviceAnalytics?.devices || overviewData.deviceStats || [];
        const totalDeviceSessions = deviceData.reduce((sum, device) => sum + (device.count || device.sessions || 0), 0);
        const deviceColors = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

        setDeviceStats(deviceData.map((device, index) => ({
          name: (device.device || device._id || 'Unknown')?.charAt(0).toUpperCase() + (device.device || device._id || 'Unknown')?.slice(1),
          value: totalDeviceSessions > 0 ? Math.round(((device.count || device.sessions || 0) / totalDeviceSessions) * 100) : 0,
          color: deviceColors[index % deviceColors.length]
        })));

        // Set client data - use clients analytics if available, otherwise fallback to overview
        setTopClients(clientsData?.clients || overviewData.topClients || []);
        setRecentActivity(activityData.activities || []);
        setUniqueUsers(uniqueUsersData);
        setBusinessMetrics(deviceAnalytics?.metrics || null);

      } catch (err) {
        console.error('Error fetching dashboard data:', err);
        setError(err.message);
        // Set empty data instead of mockup data
        setDashboardData({
          totalTryOns: 0,
          totalClients: 0,
          activeClients: 0,
          activeUsers: 0,
          trends: [],
          tryOnsGrowth: 0,
          clientsGrowth: 0,
          usersGrowth: 0
        });
        setDeviceStats([]);
        setTopClients([]);
        setRecentActivity([]);
        setUniqueUsers(null);
        setBusinessMetrics(null);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, [timeRange]);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  // Remove mockup data
  const tryOnTrends = dashboardData?.trends || [];
  const clientPerformance = topClients || [];

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50">
        <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
        <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />
        <main className={`${mainMargin} pt-16 transition-all duration-300`}>
          <div className="p-4 md:p-6">
            <div className="flex items-center justify-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  // Error state
  if (error && !dashboardData) {
    return (
      <div className="min-h-screen bg-gray-50">
        <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
        <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />
        <main className={`${mainMargin} pt-16 transition-all duration-300`}>
          <div className="p-4 md:p-6">
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <div className="flex">
                <div className="ml-3">
                  <h3 className="text-sm font-medium text-red-800">Error loading dashboard</h3>
                  <div className="mt-2 text-sm text-red-700">
                    <p>{error}</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </main>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-16 transition-all duration-300`}>
        <div className="p-4 md:p-6">
          {/* Page Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Virtual Try-On Analytics</h1>
              <p className="text-gray-600">Monitor your platform performance and client success metrics</p>
            </div>
            <div className="mt-4 md:mt-0">
              <div className="inline-flex rounded-lg border border-gray-200 p-1">
                {['7d', '30d', '90d', '1y'].map((range) => (
                  <button
                    key={range}
                    onClick={() => setTimeRange(range)}
                    className={`px-3 py-1 text-sm font-medium rounded-md ${
                      timeRange === range
                        ? 'bg-[#2D8C88] text-white'
                        : 'text-gray-600 hover:text-gray-900'
                    }`}
                  >
                    {range}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Enhanced Stats Grid */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-6">
            {/* Total Try-Ons */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{dashboardData?.totalTryOns?.toLocaleString() || '0'}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-[#2D8C88]" />
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${dashboardData?.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {dashboardData?.tryOnsGrowth >= 0 ? '+' : ''}{dashboardData?.tryOnsGrowth?.toFixed(1) || '0'}%
                </span>
                <span className="text-sm text-gray-600 ml-2">from last week</span>
              </div>
            </motion.div>

            {/* Active Clients */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{dashboardData?.activeClients?.toLocaleString() || '0'}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${dashboardData?.clientsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {dashboardData?.clientsGrowth >= 0 ? '+' : ''}{dashboardData?.clientsGrowth?.toFixed(1) || '0'}%
                </span>
                <span className="text-sm text-gray-600 ml-2">from last month</span>
              </div>
            </motion.div>

            {/* Unique Users */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Users (by IP)</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{dashboardData?.activeUsers?.toLocaleString() || '0'}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Globe className="h-6 w-6 text-purple-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-gray-600">
                  Avg {uniqueUsers?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} sessions/user
                </span>
              </div>
            </motion.div>
          </div>

          {/* Charts Grid */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            {/* Try-On Trends */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.5 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Try-On Trends</h3>
              <div className="h-80">
                <ResponsiveContainer width="100%" height="100%">
                  <LineChart data={tryOnTrends}>
                    <CartesianGrid strokeDasharray="3 3" />
                    <XAxis dataKey="date" />
                    <YAxis />
                    <Tooltip />
                    <Line
                      type="monotone"
                      dataKey="tryOns"
                      stroke="#2D8C88"
                      strokeWidth={2}
                      dot={{ fill: '#2D8C88' }}
                      name="Try-Ons"
                    />
                    <Line
                      type="monotone"
                      dataKey="conversions"
                      stroke="#3B82F6"
                      strokeWidth={2}
                      dot={{ fill: '#3B82F6' }}
                      name="Conversions"
                    />
                  </LineChart>
                </ResponsiveContainer>
              </div>
            </motion.div>

            {/* Device Distribution */}
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.6 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <h3 className="text-lg font-medium text-gray-900 mb-4">Device Distribution</h3>
              <div className="h-80">
                {deviceStats.length > 0 ? (
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={deviceStats}
                        cx="50%"
                        cy="50%"
                        labelLine={false}
                        outerRadius={80}
                        fill="#8884d8"
                        dataKey="value"
                        label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                      >
                        {deviceStats.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip 
                        formatter={(value) => [`${value}%`, 'Percentage']}
                        labelFormatter={(label) => `${label} Devices`}
                      />
                    </PieChart>
                  </ResponsiveContainer>
                ) : (
                  <div className="h-full flex items-center justify-center">
                    <p className="text-gray-500">No device data available</p>
                  </div>
                )}
              </div>
              <div className="mt-4 grid grid-cols-3 gap-4">
                {deviceStats.map((device, index) => (
                  <div key={index} className="text-center">
                    <div className="flex items-center justify-center space-x-2">
                      <div className="w-3 h-3 rounded-full" style={{ backgroundColor: device.color }}></div>
                      <span className="text-sm font-medium text-gray-900">{device.name}</span>
                    </div>
                    <p className="text-lg font-semibold text-gray-900 mt-1">{device.value}%</p>
                  </div>
                ))}
              </div>
            </motion.div>
          </div>

          {/* Top Performing Clients */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.7 }}
            className="bg-white rounded-xl shadow-sm overflow-hidden mb-6"
          >
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Top Performing Clients</h2>
            </div>
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Try-Ons</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg Session Duration</th>
                    <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Status</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {clientPerformance.length > 0 ? clientPerformance.map((client, index) => (
                    <tr key={index} className="hover:bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <div className="flex-shrink-0 h-10 w-10">
                            <div className="h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                              {(client.companyName || client.clientName || 'Unknown').charAt(0)}
                            </div>
                          </div>
                          <div className="ml-4">
                            <div className="text-sm font-medium text-gray-900">{client.companyName || client.clientName || 'Unknown Client'}</div>
                            <div className="text-xs text-gray-500">
                              ID: {client.clientId || client._id || 'N/A'}
                              {client.productType && (
                                <span className="ml-2">• {client.productType}</span>
                              )}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {(client.totalTryOns || client.sessions || 0).toLocaleString()}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        {client.avgDuration ? `${Math.round(client.avgDuration / 1000)}s` : 'N/A'}
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                          client.isActive ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {client.isActive ? 'Active' : 'Inactive'}
                        </span>
                      </td>
                    </tr>
                  )) : (
                    <tr>
                      <td colSpan="4" className="px-6 py-4 text-center text-sm text-gray-500">
                        No client performance data available
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </motion.div>

          {/* Recent Activity */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.8 }}
            className="bg-white rounded-xl shadow-sm overflow-hidden"
          >
            <div className="p-6 border-b border-gray-200">
              <h2 className="text-lg font-medium text-gray-900">Recent Activity</h2>
            </div>
            <div className="divide-y divide-gray-200">
              {recentActivity.length > 0 ? recentActivity.map((activity, index) => (
                <div key={index} className="p-6">
                  <div className="flex items-center space-x-4">
                    <div className="w-10 h-10 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                      {(activity.type === 'new_client' || activity.action?.includes('client')) && <Users className="h-6 w-6 text-[#2D8C88]" />}
                      {(activity.type === 'high_volume' || activity.action?.includes('session')) && <Eye className="h-6 w-6 text-blue-500" />}
                      {(activity.type === 'conversion' || activity.action?.includes('conversion')) && <Activity className="h-6 w-6 text-green-500" />}
                      {!activity.type && !activity.action && <Activity className="h-6 w-6 text-gray-500" />}
                    </div>
                    <div>
                      <p className="text-sm font-medium text-gray-900">{activity.title || activity.action || 'Unknown activity'}</p>
                      <p className="text-sm text-gray-500">{activity.description || activity.user || 'No description'}</p>
                    </div>
                    <div className="ml-auto">
                      <p className="text-sm text-gray-500">{activity.timestamp || activity.time || 'Unknown time'}</p>
                    </div>
                  </div>
                </div>
              )) : (
                <div className="p-6 text-center text-sm text-gray-500">
                  No recent activity available
                </div>
              )}
            </div>
          </motion.div>
        </div>
      </main>
    </div>
  );
};

export default AdminDashboard; 