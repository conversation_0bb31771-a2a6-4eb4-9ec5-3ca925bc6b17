import React, { useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

const TryOnAnalytics = () => {
  const navigate = useNavigate();

  useEffect(() => {
    // Redirect to the new comprehensive analytics
    navigate('/admin/analytics/tryon', { replace: true });
  }, [navigate]);

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Redirecting to comprehensive analytics...</p>
      </div>
    </div>
  );
};

export default TryOnAnalytics;