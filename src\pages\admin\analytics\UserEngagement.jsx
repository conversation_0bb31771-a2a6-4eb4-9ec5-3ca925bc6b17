import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  AreaChart,
  Area,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar
} from 'recharts';
import { Users, Clock, Calendar, TrendingUp } from 'lucide-react';

const UserEngagement = ({ timeRange = '30d' }) => {
  const [loading, setLoading] = useState(true);
  const [engagementData, setEngagementData] = useState({
    dailyTrends: [],
    hourlyPatterns: [],
    weeklyPatterns: [],
    durationDistribution: {}
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');

        if (!token) {
          console.error('No authentication token found');
          return;
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 30);
        }

        const response = await fetch(`${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const data = await response.json();
          setEngagementData(data);
        } else {
          console.error('Failed to fetch engagement data');
        }
      } catch (error) {
        console.error('Error fetching engagement data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  // Format data for charts
  const hourlyData = engagementData.hourlyPatterns?.map(item => ({
    hour: `${item._id.toString().padStart(2, '0')}:00`,
    sessions: item.sessions,
    avgDuration: Math.round(item.avgDuration || 0)
  })) || [];

  const weeklyData = engagementData.weeklyPatterns?.map(item => {
    const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
    return {
      day: dayNames[item._id - 1] || 'Unknown',
      sessions: item.sessions,
      avgDuration: Math.round(item.avgDuration || 0)
    };
  }) || [];

  const dailyTrends = engagementData.dailyTrends?.map(item => ({
    date: item._id,
    sessions: item.sessions,
    avgDuration: Math.round(item.avgDuration || 0)
  })) || [];

  // Calculate session duration distribution data
  const sessionDurationData = [
    { name: '0-30s', value: engagementData.durationDistribution?.['0-30'] || 0 },
    { name: '30s-1m', value: engagementData.durationDistribution?.['30-60'] || 0 },
    { name: '1-2m', value: engagementData.durationDistribution?.['60-120'] || 0 },
    { name: '2-5m', value: engagementData.durationDistribution?.['120-300'] || 0 },
    { name: '5m+', value: engagementData.durationDistribution?.['300+'] || 0 }
  ];

  // Calculate metrics for display
  const totalSessions = dailyTrends.reduce((sum, day) => sum + (day.sessions || 0), 0);
  const avgDuration = dailyTrends.reduce((sum, day) => sum + (day.avgDuration || 0), 0) / (dailyTrends.length || 1);
  const peakDay = weeklyData.reduce((max, current) =>
    current.sessions > (max.sessions || 0) ? current : max, {});
  const peakHour = hourlyData.reduce((max, current) =>
    current.sessions > (max.sessions || 0) ? current : max, {});

  const formatDuration = (seconds) => {
    if (!seconds) return '0s';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return minutes > 0 ? `${minutes}m ${remainingSeconds}s` : `${remainingSeconds}s`;
  };

  const metrics = [
    {
      title: 'Total Sessions',
      value: totalSessions.toLocaleString(),
      icon: <Users className="h-6 w-6 text-blue-500" />
    },
    {
      title: 'Avg. Duration',
      value: formatDuration(avgDuration),
      icon: <Clock className="h-6 w-6 text-green-500" />
    },
    {
      title: 'Peak Day',
      value: peakDay.day || 'N/A',
      icon: <Calendar className="h-6 w-6 text-purple-500" />
    },
    {
      title: 'Peak Hour',
      value: peakHour.hour || 'N/A',
      icon: <TrendingUp className="h-6 w-6 text-[#2D8C88]" />
    }
  ];

  // Colors for charts
  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#6366F1', '#F59E0B'];

  if (loading) {
    return (
      <div className="p-6">
        <div className="animate-pulse">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
            <div className="bg-white rounded-xl shadow-sm p-6">
              <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
              <div className="h-96 bg-gray-200 rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        {metrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-sm p-6"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-2xl font-semibold text-gray-900 mt-1">{metric.value}</p>
              </div>
              <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                {metric.icon}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Daily Trends and Weekly Pattern */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Daily Trends</h3>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <AreaChart data={dailyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  fill="#2D8C88"
                  fillOpacity={0.2}
                />
                <Area
                  type="monotone"
                  dataKey="avgDuration"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.2}
                />
              </AreaChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Pattern</h3>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={weeklyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="day" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sessions" fill="#2D8C88" />
                <Bar dataKey="avgDuration" fill="#3B82F6" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Hourly Distribution and Duration Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Hourly Distribution</h3>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={hourlyData}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                />
                <Line
                  type="monotone"
                  dataKey="avgDuration"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Session Duration Distribution</h3>
          <div className="h-96">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={sessionDurationData}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={150}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {sessionDurationData.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>

      {/* Weekly Pattern Radar Chart */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="bg-white rounded-xl shadow-sm p-6"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Weekly Pattern Analysis</h3>
        <div className="h-96">
          <ResponsiveContainer width="100%" height="100%">
            <RadarChart data={weeklyData}>
              <PolarGrid />
              <PolarAngleAxis dataKey="day" />
              <PolarRadiusAxis />
              <Radar
                name="Sessions"
                dataKey="sessions"
                stroke="#2D8C88"
                fill="#2D8C88"
                fillOpacity={0.6}
              />
              <Radar
                name="Avg Duration"
                dataKey="avgDuration"
                stroke="#3B82F6"
                fill="#3B82F6"
                fillOpacity={0.6}
              />
              <Legend />
              <Tooltip />
            </RadarChart>
          </ResponsiveContainer>
        </div>
      </motion.div>
    </div>
  );
};

export default UserEngagement;
