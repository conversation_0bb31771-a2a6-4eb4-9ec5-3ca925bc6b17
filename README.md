# Via Try-On Frontend

This is the frontend application for the Via Try-On platform, which allows users to virtually try on watches and bracelets using their camera or uploaded photos.

## Features

- Virtual try-on for watches and bracelets
- Camera integration for real-time try-on
- Photo upload for try-on on existing images
- Product browsing and filtering
- Detailed product views
- Image capture and saving

## Setup

1. Install dependencies:
```
npm install
```

2. Start the development server:
```
npm start
```

The application will be available at http://localhost:3000.

## Backend Integration

The frontend integrates with the backend server for image storage and processing. To enable full functionality:

1. Start the backend server (see backend README)
2. Ensure the API_URL in `src/services/api.js` points to your backend server

## Project Structure

- `src/pages/` - Main page components
  - `Home.jsx` - Home page
  - `Watches.jsx` - Watches collection page
  - `Bracelets.jsx` - Bracelets collection page
  - `ProductDetails.jsx` - Product detail page
  - `VirtualTryOn.jsx` - Virtual try-on page
- `src/components/` - Reusable components
  - `Navbar.jsx` - Navigation bar
  - `Footer.jsx` - Footer component
- `src/services/` - API services
  - `api.js` - Backend API integration

## Camera Access

For camera access to work properly:
- Use a secure context (HTTPS or localhost)
- Ensure camera permissions are granted in the browser
- For mobile devices, ensure the site has camera permissions

## Available Scripts

### `npm start`

Runs the app in the development mode.\
Open [http://localhost:3000](http://localhost:3000) to view it in your browser.

### `npm test`

Launches the test runner in the interactive watch mode.

### `npm run build`

Builds the app for production to the `build` folder.
