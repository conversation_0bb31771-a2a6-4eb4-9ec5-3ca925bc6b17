import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON>Chart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell
} from 'recharts';
import { toast } from 'react-toastify';
import { Eye, Clock, TrendingUp, Tag } from 'lucide-react';

const ProductAnalytics = ({ timeRange }) => {
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [productData, setProductData] = useState(null);

  useEffect(() => {
    const fetchProductData = async () => {
      try {
        setLoading(true);
        setError(null);

        const token = localStorage.getItem('token');
        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        console.log('Fetching product analytics with params:', {
          start: start.toISOString(),
          end: end.toISOString(),
          apiUrl: `${apiUrl}/api/analytics/admin/product`
        });

        const response = await fetch(`${apiUrl}/api/analytics/admin/product?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          console.error('API Error Response:', {
            status: response.status,
            statusText: response.statusText,
            errorData
          });
          throw new Error(`Failed to fetch product analytics data: ${response.status} ${response.statusText}`);
        }

        const data = await response.json();
        console.log('Received product analytics data:', data);

        if (!data || Object.keys(data).length === 0) {
          console.warn('Received empty data from API');
        }

        // Calculate summary metrics from the product data array
        const totalProducts = data.products?.length || 0;
        const totalTryOns = data.products?.reduce((sum, product) => sum + (product.views || 0), 0) || 0;
        const avgTryOnDuration = data.products?.length > 0 ?
          data.products.reduce((sum, product) => sum + (product.avgViewDuration || 0), 0) / data.products.length : 0;


        // Group by category for category performance
        const categoryMap = {};
        (data.products || []).forEach(product => {
          const category = product.category || 'Unknown';
          if (!categoryMap[category]) {
            categoryMap[category] = {
              category,
              tryOns: 0,
              conversions: 0,
              count: 0
            };
          }
          categoryMap[category].tryOns += product.views || 0;
          categoryMap[category].conversions += product.conversions || 0;
          categoryMap[category].count += 1;
        });

        const categoryPerformance = Object.values(categoryMap).map(cat => ({
          ...cat,
          conversionRate: cat.tryOns > 0 ? (cat.conversions / cat.tryOns) * 100 : 0
        }));

        setProductData({
          products: data.products || [],
          totalProducts,
          totalTryOns,
          avgTryOnDuration,
          topProducts: (data.products || []).slice(0, 10).map(product => ({
            name: product.name || 'Unknown Product',
            category: product.category || 'Unknown',
            tryOns: product.views,
            avgDuration: product.avgViewDuration
          })),
          categoryPerformance,
          // Growth metrics (set to 0 since we don't have previous period data)
          productsGrowth: 0,
          tryOnsGrowth: 0,
          durationGrowth: 0
        });
      } catch (err) {
        console.error('Error fetching product analytics:', err);
        setError(err.message);
        toast.error(`Error loading analytics: ${err.message}`);
      } finally {
        setLoading(false);
      }
    };

    fetchProductData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88]"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <div className="flex">
          <div className="ml-3">
            <h3 className="text-sm font-medium text-red-800">Error loading product analytics</h3>
            <div className="mt-2 text-sm text-red-700">
              <p>{error}</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Products</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {productData?.totalProducts?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
              <Tag className="h-6 w-6 text-[#2D8C88]" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${productData?.productsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {productData?.productsGrowth >= 0 ? '+' : ''}{productData?.productsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {productData?.totalTryOns?.toLocaleString() || '0'}
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <Eye className="h-6 w-6 text-blue-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${productData?.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {productData?.tryOnsGrowth >= 0 ? '+' : ''}{productData?.tryOnsGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Avg. Try-On Duration</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">
                {Math.round(productData?.avgTryOnDuration || 0)}s
              </p>
            </div>
            <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
              <Clock className="h-6 w-6 text-green-500" />
            </div>
          </div>
          <div className="mt-4">
            <span className={`text-sm font-medium ${productData?.durationGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
              {productData?.durationGrowth >= 0 ? '+' : ''}{productData?.durationGrowth?.toFixed(1) || '0'}%
            </span>
            <span className="text-sm text-gray-600 ml-2">from previous period</span>
          </div>
        </motion.div>


      </div>

      {/* Product Performance Table */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
        className="bg-white rounded-xl shadow-sm overflow-hidden"
      >
        <div className="p-6 border-b border-gray-200">
          <h2 className="text-lg font-medium text-gray-900">Top Performing Products</h2>
        </div>
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Product</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Try-Ons</th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Avg. Duration</th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {productData?.topProducts?.map((product, index) => (
                <tr key={index} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="flex-shrink-0 h-10 w-10">
                        <div className="h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                          {product.name.charAt(0)}
                        </div>
                      </div>
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">{product.name}</div>
                        <div className="text-sm text-gray-500">{product.category}</div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {product.tryOns?.toLocaleString()}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {Math.round(product.avgDuration)}s
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </motion.div>

      {/* Category Performance Chart - Only show if we have data */}
      {productData?.categoryPerformance && productData.categoryPerformance.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Category Performance</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={productData.categoryPerformance}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="category" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="tryOns" fill="#2D8C88" name="Try-Ons" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}

      {/* Category Distribution by Try-Ons */}
      {productData?.categoryPerformance && productData.categoryPerformance.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Try-Ons by Category</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={productData.categoryPerformance}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="tryOns"
                  label={({ category, percent }) => `${category} ${(percent * 100).toFixed(0)}%`}
                >
                  {productData.categoryPerformance.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#8B5CF6'][index % 5]} />
                  ))}
                </Pie>
                <Tooltip formatter={(value) => [`${value.toLocaleString()}`, 'Try-Ons']} />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      )}
    </div>
  );
};

export default ProductAnalytics;
