# Adding New Watches to ViaTryon

This guide explains how to add new watches to the ViaTryon system.

## Quick Start

1. **Add your watch image** to the `public/imgs/watches/` folder
   - Use the naming convention: `watch_X.png` (where X is the next number)
   - For example: `watch_7.png`, `watch_8.png`, etc.

2. **Update the watchFiles array** in `src/utils/imageLoader.js`
   - Add the filename to the `watchFiles` array
   - Optionally add custom names, types, and brands

3. **That's it!** The system will automatically:
   - Load your new watch
   - Generate appropriate metadata
   - Make it available for try-on
   - Display it on the Watches page and ProductShowcase

## Detailed Steps

### Step 1: Add Watch Image
Place your watch image in `public/imgs/watches/` with the naming convention:
```
watch_7.png
watch_8.png
watch_9.png
```

### Step 2: Update the watchFiles Array
Edit `src/utils/imageLoader.js` and add your filename to the `watchFiles` array:

```javascript
const watchFiles = [
  'watch_1.png',
  'watch_2.png', 
  'watch_3.png',
  'watch_4.png',
  'watch_5.png',
  'watch_6.png',
  'watch_7.png',  // Add your new watch here
  'watch_8.png'   // Add more watches as needed
];
```

### Step 3: Optional - Add Custom Names/Types/Brands
If you want custom names, types, or brands for your watches, you can add them to the respective arrays:

```javascript
// Generate watch names based on index
const watchNames = [
  'Classic Black',
  'Silver Chrono', 
  'Gold Luxury',
  'Rose Gold',
  'Minimalist',
  'Sport Blue',
  'Your Custom Name',  // Add custom name here
  'Another Custom Name'
];

// Generate watch types
const watchTypes = [
  'dress',
  'sport', 
  'luxury',
  'fashion',
  'minimalist',
  'smartwatch',
  'your-custom-type',  // Add custom type here
  'another-type'
];

// Generate brands
const watchBrands = [
  'Rolex',
  'Omega', 
  'Seiko',
  'Citizen',
  'Fossil',
  'Tissot',
  'Your Brand',  // Add custom brand here
  'Another Brand'
];
```

### Step 4: Test Your Changes
1. Start the development server: `npm start`
2. Navigate to the Watches page or ProductShowcase
3. Your new watch should appear in the grid
4. Try the AR try-on feature with your new watch

## Automatic Metadata Generation

If you don't provide custom names, types, or brands, the system will automatically generate:
- **Names**: "Watch X" (where X is the watch number)
- **Types**: Rotates through dress, sport, luxury, fashion, minimalist, smartwatch
- **Brands**: Rotates through Rolex, Omega, Seiko, Citizen, Fossil, Tissot
- **Dimensions**: Automatically varied for realistic sizing
- **Prices**: Base price + increment per watch

## Image Requirements

- **Format**: PNG (recommended) or JPG
- **Background**: White or transparent background works best
- **Size**: Recommended minimum 400x400px
- **Quality**: High quality for best AR try-on results

## Troubleshooting

### Watch not appearing?
1. Check that the filename is added to `watchFiles` array
2. Verify the image file exists in `public/imgs/watches/`
3. Check browser console for any errors

### Try-on not working?
1. Ensure the image has a clean background
2. Check that the image dimensions are reasonable
3. Verify the file path is correct

### Performance issues?
1. Optimize image size (compress if needed)
2. Consider using WebP format for better compression
3. Ensure images aren't too large (>2MB)

## Files Updated

The following files now use dynamic watch loading:
- `src/pages/Watches.jsx` - Shows all watches from dynamic loading
- `src/pages/ProductShowcase.jsx` - Shows all watches in the showcase
- `src/pages/VirtualTryOn.jsx` - Shows all watches in the try-on interface

## Support

If you encounter any issues, check:
1. Browser console for errors
2. Network tab for failed image loads
3. File permissions and paths
4. Image format compatibility 