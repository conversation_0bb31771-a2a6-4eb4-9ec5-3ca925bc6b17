import React, { useState, useEffect } from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { motion } from 'framer-motion';
import './Blog.css';

const categories = [
  { id: 'all', name: 'All Posts' },
  { id: 'technology', name: 'Technology' },
  { id: 'business', name: 'Business' },
  { id: 'guides', name: 'Guides' },
  { id: 'case-studies', name: 'Case Studies' }
];

const BlogPost = () => {
  const { id } = useParams();
  const [post, setPost] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchBlogPost = async () => {
      try {
        setLoading(true);
        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        const response = await fetch(`${apiUrl}/api/blogs/${id}`);

        if (!response.ok) {
          throw new Error('Blog post not found');
        }

        const data = await response.json();
        setPost(data.blog);
      } catch (err) {
        console.error('Error fetching blog post:', err);
        setError(err.message);
      } finally {
        setLoading(false);
      }
    };

    fetchBlogPost();
  }, [id]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 overflow-x-hidden">
        <Navbar />
        <div className="container mx-auto px-4 py-32 text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#2D8C88] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading blog post...</p>
        </div>
        <Footer />
      </div>
    );
  }

  if (error || !post) {
    return (
      <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 overflow-x-hidden">
        <Navbar />
        <div className="container mx-auto px-4 py-32 text-center">
          <h1 className="text-3xl font-serif mb-4 text-red-600">
            {error ? 'Error Loading Post' : 'Post Not Found'}
          </h1>
          {error && <p className="text-gray-600 mb-4">{error}</p>}
          <Link to="/blog" className="text-[#2D8C88] hover:text-[#F28C38] underline">
            Back to Blog
          </Link>
        </div>
        <Footer />
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 overflow-x-hidden">
      <Navbar />
      <section className="relative min-h-[40vh] flex items-center pt-24 pb-12 md:pt-32 md:pb-16 overflow-hidden blog-hero">
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="max-w-3xl mx-auto text-center">
            <motion.div initial={{ opacity: 0, y: 20 }} animate={{ opacity: 1, y: 0 }} transition={{ duration: 0.8 }}>
              <span className="inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4">
                {categories.find(cat => cat.id === post.category)?.name}
              </span>
              <h1 className="text-4xl sm:text-5xl md:text-6xl font-serif text-gray-900 mb-6 leading-tight">
                {post.title}
              </h1>
              <div className="flex flex-col sm:flex-row items-center justify-center gap-4 mb-6 text-gray-500 text-sm">
                <span>
                  {new Date(post.date || post.createdAt).toLocaleDateString('en-US', {
                    year: 'numeric',
                    month: 'long',
                    day: 'numeric'
                  })}
                </span>
                <span>•</span>
                <span>{post.readTime || '5 min read'}</span>
                <span>•</span>
                <span>By {post.author}</span>
              </div>
            </motion.div>
          </div>
        </div>
      </section>
      <section className="container mx-auto px-4 md:px-6 py-8 md:py-16">
        <div className="max-w-3xl mx-auto bg-white rounded-xl shadow-md p-6 md:p-12 blog-card">
          <img src={post.image} alt={post.title} className="w-full h-64 object-cover rounded-lg mb-8" />
          <div className="prose prose-lg max-w-none text-gray-800 mb-8 whitespace-pre-line">
            {post.content}
          </div>
          <Link to="/blog" className="blog-read-more text-lg">← Back to Blog</Link>
        </div>
      </section>
      <Footer />
    </div>
  );
};

export default BlogPost; 