import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  Legend
} from 'recharts';
import { toast } from 'react-toastify';

const GeographicAnalytics = ({ timeRange }) => {
  const [data, setData] = useState({
    metrics: {
      totalCountries: 0,
      countryChange: 0,
      totalSessions: 0,
      sessionChange: 0,
      totalConversions: 0,
      conversionChange: 0,
      totalUniqueUsers: 0,
      userChange: 0
    },
    countries: [],
    continents: [],
    cities: [],
    countryDistribution: [],
    continentDistribution: []
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        setError(null);
        const token = localStorage.getItem('token');

        if (!token) {
          throw new Error('No authentication token found');
        }

        const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range based on timeRange
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 30);
        }

        console.log('Fetching geographic analytics with params:', {
          start: start.toISOString(),
          end: end.toISOString(),
          apiUrl: `${apiUrl}/api/analytics/admin/geographic`
        });

        const response = await fetch(`${apiUrl}/api/analytics/admin/geographic?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (!response.ok) {
          const errorData = await response.json().catch(() => null);
          console.error('API Error Response:', {
            status: response.status,
            statusText: response.statusText,
            errorData
          });
          throw new Error(`Failed to fetch geographic data: ${response.status} ${response.statusText}`);
        }

        const result = await response.json();
        console.log('Received geographic analytics data:', result);
        console.log('Countries data:', result.countries);
        console.log('Continents data:', result.continents);

        // Check if we have any data at all
        const hasCountryData = result.countries && result.countries.length > 0;
        const hasContinentData = result.continents && result.continents.length > 0;

        console.log('Has country data:', hasCountryData);
        console.log('Has continent data:', hasContinentData);

        // Transform the backend data to match frontend expectations
        const transformedData = {
          metrics: {
            totalCountries: result.countries?.length || 0,
            countryChange: 0, // No previous period data available
            totalSessions: result.countries?.reduce((sum, country) => sum + (country.sessions || 0), 0) || 0,
            sessionChange: 0, // No previous period data available
            totalUniqueUsers: result.countries?.reduce((sum, country) => sum + (country.uniqueUsers || 0), 0) || 0,
            userChange: 0 // No previous period data available
          },
          countries: result.countries?.map(country => ({
            name: country.country,
            sessions: country.sessions,
            uniqueUsers: country.uniqueUsers
          })) || [],
          continents: result.continents?.map(continent => ({
            name: continent.continent,
            sessions: continent.sessions,
            uniqueUsers: continent.uniqueUsers
          })) || [],
          cities: [], // Not available since city data is null in sessions
          countryDistribution: result.countries?.map(country => ({
            country: country.country,
            value: country.sessions
          })) || [],
          continentDistribution: result.continents?.map(continent => ({
            continent: continent.continent,
            value: continent.sessions
          })) || []
        };

        console.log('Transformed data:', transformedData);

        // If no data is available, show a message but still set the empty data structure
        if (transformedData.countries.length === 0 && transformedData.continents.length === 0) {
          console.log('No geographic data available - this might indicate:');
          console.log('1. No sessions in the selected time range');
          console.log('2. Sessions do not have location.timezone data');
          console.log('3. Sessions do not have location.userIP or location.ip data');
        }

        setData(transformedData);
      } catch (error) {
        console.error('Error fetching geographic analytics:', error);
        setError(error.message);
        toast.error(error.message || 'Failed to load analytics data');
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-teal-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center h-64">
        <div className="text-red-600 mb-4">
          <svg className="h-12 w-12" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
          </svg>
        </div>
        <p className="text-lg font-medium text-gray-900">{error}</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-teal-600 text-white rounded-lg hover:bg-teal-700 transition-colors"
        >
          Retry
        </button>
      </div>
    );
  }

  const keyMetrics = [
    {
      title: 'Total Countries',
      value: data.metrics?.totalCountries?.toLocaleString() || '0',
      change: `${(data.metrics?.countryChange || 0) >= 0 ? '+' : ''}${(data.metrics?.countryChange || 0).toFixed(1)}%`,
      trend: (data.metrics?.countryChange || 0) >= 0 ? 'up' : 'down',
      icon: (
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3.055 11H5a2 2 0 012 2v1a2 2 0 002 2 2 2 0 012 2v2.945M8 3.935V5.5A2.5 2.5 0 0010.5 8h.5a2 2 0 012 2 2 2 0 104 0 2 2 0 012-2h1.064M15 20.488V18a2 2 0 012-2h3.064M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
      ),
    },
    {
      title: 'Total Sessions',
      value: data.metrics?.totalSessions?.toLocaleString() || '0',
      change: `${(data.metrics?.sessionChange || 0) >= 0 ? '+' : ''}${(data.metrics?.sessionChange || 0).toFixed(1)}%`,
      trend: (data.metrics?.sessionChange || 0) >= 0 ? 'up' : 'down',
      icon: (
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
        </svg>
      ),
    },
    {
      title: 'Unique Users (by IP)',
      value: data.metrics?.totalUniqueUsers?.toLocaleString() || '0',
      change: `${(data.metrics?.userChange || 0) >= 0 ? '+' : ''}${(data.metrics?.userChange || 0).toFixed(1)}%`,
      trend: (data.metrics?.userChange || 0) >= 0 ? 'up' : 'down',
      icon: (
        <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z" />
        </svg>
      ),
    }
  ];

  const COLORS = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8'];

  return (
    <div className="space-y-6">
      {/* Key Metrics */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6">
        {keyMetrics.map((metric, index) => (
          <motion.div
            key={metric.title}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
            className="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100"
          >
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">{metric.title}</p>
                <p className="text-xl sm:text-2xl font-bold text-gray-900 mt-1">{metric.value}</p>
                <div className="flex items-center mt-2">
                  <span className={`text-sm font-medium ${
                    metric.trend === 'up' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {metric.change}
                  </span>
                  <span className="text-sm text-gray-500 ml-2">vs last period</span>
                </div>
              </div>
              <div className="p-3 bg-teal-50 rounded-lg">
                {metric.icon}
              </div>
            </div>
          </motion.div>
        ))}
      </div>

      {/* Geographic Distribution */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Country Distribution</h3>
          <div className="h-64 sm:h-80">
            {data.countryDistribution && data.countryDistribution.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={data.countryDistribution}
                    dataKey="value"
                    nameKey="country"
                    cx="50%"
                    cy="50%"
                    outerRadius={80}
                    label
                  >
                    {data.countryDistribution.map((_, index) => (
                      <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                    ))}
                  </Pie>
                  <Tooltip />
                  <Legend />
                </PieChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">No country distribution data available</p>
              </div>
            )}
          </div>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">Top Countries</h3>
          <div className="h-64 sm:h-80">
            {data.countries && data.countries.length > 0 ? (
              <ResponsiveContainer width="100%" height="100%">
                <BarChart data={data.countries}>
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis 
                    dataKey="name" 
                    angle={-45}
                    textAnchor="end"
                    height={80}
                    fontSize={12}
                    interval={0}
                  />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="sessions" fill="#0D9488" name="Sessions" />
                  <Bar dataKey="uniqueUsers" fill="#8B5CF6" name="Unique Users" />
                </BarChart>
              </ResponsiveContainer>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-gray-500">No country data available</p>
              </div>
            )}
          </div>
        </motion.div>
      </div>

      {/* Continent Distribution */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7 }}
        className="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Continent Distribution</h3>
        <div className="h-64 sm:h-80">
          {data.continents && data.continents.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={data.continents}
                  dataKey="sessions"
                  nameKey="name"
                  cx="50%"
                  cy="50%"
                  outerRadius={80}
                  label
                >
                  {data.continents.map((_, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
                <Legend />
              </PieChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">No continent data available</p>
            </div>
          )}
        </div>
      </motion.div>

      {/* Unique Users by Country */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.8 }}
        className="bg-white rounded-xl shadow-sm p-4 sm:p-6 border border-gray-100"
      >
        <h3 className="text-lg font-medium text-gray-900 mb-4">Unique Users by Country</h3>
        <div className="h-64 sm:h-80">
          {data.countries && data.countries.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={data.countries}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis
                  dataKey="name"
                  angle={-45}
                  textAnchor="end"
                  height={80}
                  fontSize={12}
                  interval={0}
                />
                <YAxis />
                <Tooltip />
                <Legend />
                <Bar dataKey="uniqueUsers" fill="#10B981" name="Unique Users" />
                <Bar dataKey="sessions" fill="#0D9488" name="Total Sessions" />
              </BarChart>
            </ResponsiveContainer>
          ) : (
            <div className="flex items-center justify-center h-full">
              <p className="text-gray-500">No user data available</p>
            </div>
          )}
        </div>
      </motion.div>
    </div>
  );
};

export default GeographicAnalytics;
