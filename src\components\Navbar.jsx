import React, { useState, useEffect, useRef } from 'react';
import { motion } from 'framer-motion';
import { Link } from 'react-router-dom';

const Navbar = () => {
  const [scrolled, setScrolled] = useState(false);
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [activeDropdown, setActiveDropdown] = useState(null);

  // Refs for dropdown menus
  const dropdownRefs = useRef({});

  useEffect(() => {
    const handleScroll = () => {
      setScrolled(window.scrollY > 10);
    };

    const handleClickOutside = (event) => {
      if (activeDropdown && dropdownRefs.current[activeDropdown] &&
          !dropdownRefs.current[activeDropdown].contains(event.target)) {
        setActiveDropdown(null);
      }
    };

    window.addEventListener('scroll', handleScroll);
    document.addEventListener('mousedown', handleClickOutside);

    return () => {
      window.removeEventListener('scroll', handleScroll);
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [activeDropdown]);

  const user = (() => {
    try {
      const userData = localStorage.getItem('user');
      return userData ? JSON.parse(userData) : null;
    } catch (error) {
      console.error('Error parsing user data:', error);
      return null;
    }
  })();
  const isLoggedIn = !!user;
  const isAdmin = user?.role === 'admin';
  const isClient = user?.role === 'client';

  const navigation = [
    { name: 'Home', href: '/' },
    { 
      name: 'Virtual Try-On', 
      href: '/virtual-try-on',
      dropdown: [
        { name: 'Watches', href: '/watches' },
        { name: 'Bracelets', href: '/bracelets' },
        { name: 'Rings', href: '/rings' },
        { name: 'Earrings', href: '/earrings' }
      ]
    },
    { name: 'How It Works', href: '/how-it-works' },
    { name: 'Requirments', href: '/requirements' },
    { name: 'Blog', href: '/blog' },
    { name: 'Pricing', href: '/pricing' },
    { name: 'Contact', href: '/contact' }
  ];

  const menuItems = isAdmin ? [
    { path: '/admin', label: 'Dashboard', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
    { path: '/admin/clients', label: 'Clients', icon: 'M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z' },
    { path: '/admin/tryon-analytics', label: 'Analytics', icon: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z' },
    { path: '/admin/settings', label: 'Settings', icon: 'M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z' }
  ] : [
    { path: '/client/dashboard', label: 'Dashboard', icon: 'M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6' },
    { path: '/virtual-try-on', label: 'Virtual Try-On', icon: 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z' }
  ];

  return (
    <nav
      className={`fixed w-full z-50 ${
        scrolled
          ? 'bg-white shadow-lg py-2 md:py-3'
          : 'bg-white/95 backdrop-blur-md py-3 md:py-4'
      } transition-all duration-300`}
    >
      <div className="container mx-auto px-4 md:px-6">
        <div className="flex justify-between items-center">
          {/* Logo and ViaTryon Name */}
          <div className="flex items-center">
            <Link to="/" className="flex items-center">
              <img src="/imgs/logo-only.png" alt="ViaTryon" className="h-8 md:h-10 mr-2" />
              <span className="font-serif text-lg md:text-2xl font-medium text-[#1F2937] tracking-tight">
                ViaTryon
              </span>
            </Link>
          </div>

          {/* Desktop Navigation */}
          <div className="hidden md:flex items-center">
            <div className="flex space-x-8">
              {navigation.map((item) => (
                <div key={item.name} className="py-2 relative">
                  {item.dropdown ? (
                    <div className="relative">
                      <button 
                        onClick={() => setActiveDropdown(activeDropdown === item.name ? null : item.name)}
                        className="text-[#1F2937] text-base font-sans font-medium hover:text-[#F28C38] transition-colors duration-200 flex items-center"
                      >
                        {item.name}
                        <svg className={`w-4 h-4 ml-1 transition-transform duration-200 ${activeDropdown === item.name ? 'rotate-180' : ''}`} fill="none" viewBox="0 0 24 24" stroke="currentColor">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                        </svg>
                      </button>
                      {activeDropdown === item.name && (
                        <motion.div
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50"
                        >
                          {item.dropdown.map((dropdownItem) => (
                            <Link
                              key={dropdownItem.name}
                              to={dropdownItem.href}
                              className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                              onClick={() => setActiveDropdown(null)}
                            >
                              {dropdownItem.name}
                            </Link>
                          ))}
                        </motion.div>
                      )}
                    </div>
                  ) : (
                    <Link
                      to={item.href}
                      className="text-[#1F2937] text-base font-sans font-medium hover:text-[#F28C38] transition-colors duration-200 relative"
                    >
                      {item.name}
                      <span
                        className="absolute bottom-0 left-0 h-0.5 bg-[#F28C38] rounded-full"
                        style={{ width: '0%', transition: 'width 0.3s' }}
                        onMouseEnter={(e) => (e.target.style.width = '100%')}
                        onMouseLeave={(e) => (e.target.style.width = '0%')}
                      />
                    </Link>
                  )}
                </div>
              ))}
            </div>

            {isLoggedIn && (
              <div className="relative ml-6">
                <button
                  onClick={() => setActiveDropdown(activeDropdown === 'profile' ? null : 'profile')}
                  className="flex items-center space-x-2 focus:outline-none"
                >
                  <div className="w-9 h-9 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                    <span className="text-base font-semibold">
                      {user?.email ? user.email[0].toUpperCase() : 'U'}
                    </span>
                  </div>
                  <svg className={`w-4 h-4 text-gray-400 transition-transform duration-200 ${activeDropdown === 'profile' ? 'rotate-180' : ''}`} xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" /></svg>
                </button>
                {activeDropdown === 'profile' && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    className="absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg py-1 z-50"
                  >
                    {menuItems.map((item) => (
                      <Link
                        key={item.path}
                        to={item.path}
                        className="flex items-center px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-5 w-5 mr-2 text-[#2D8C88]"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d={item.icon}
                          />
                        </svg>
                        {item.label}
                      </Link>
                    ))}
                    <button
                      onClick={() => { localStorage.clear(); window.location.href = '/login'; }}
                      className="flex items-center w-full text-left px-4 py-2 text-sm text-gray-700 hover:bg-gray-100"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-2 text-[#F28C38]"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                        />
                      </svg>
                      Logout
                    </button>
                  </motion.div>
                )}
              </div>
            )}
          </div>

          {/* Mobile Menu Button */}
          <div className="md:hidden flex items-center space-x-3">
            {/* Mobile Menu Toggle */}
            <button
              onClick={() => setMobileMenuOpen(!mobileMenuOpen)}
              className="text-[#1F2937] focus:outline-none p-2 hover:bg-gray-100 rounded-lg transition-colors duration-200"
              aria-label="Toggle mobile menu"
            >
              <svg
                className="h-6 w-6"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                {mobileMenuOpen ? (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M6 18L18 6M6 6l12 12"
                  />
                ) : (
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                )}
              </svg>
            </button>
          </div>

          {/* Desktop Auth Buttons */}
          <div className="hidden md:flex items-center space-x-5">
            {/* Login Button - Only show when not logged in */}
            {!isLoggedIn && (
              <>
                <Link to="/login">
                  <button
                    className="px-5 py-2.5 rounded-full font-sans font-medium text-sm transition-all duration-200 border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white"
                  >
                    Login
                  </button>
                </Link>

                {/* Schedule Demo Button */}
                <Link to="/schedule-demo">
                  <button
                    className="bg-[#F28C38] text-white px-5 py-2.5 rounded-full font-sans font-medium text-sm transition-all duration-200 hover:bg-[#e07c28]"
                  >
                    Demo
                  </button>
                </Link>
              </>
            )}
          </div>
        </div>

        {/* Mobile Menu */}
        {mobileMenuOpen && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
            className="md:hidden mt-4 space-y-2 pb-6 border-t border-[#E5E7EB] pt-4"
          >
            {navigation.map((item) => (
              <div key={item.name}>
                {item.dropdown ? (
                  <div>
                    <div className="px-4 py-3 text-[#1F2937] font-sans font-medium text-base">
                      {item.name}
                    </div>
                    <div className="ml-4 space-y-1">
                      {item.dropdown.map((dropdownItem) => (
                        <Link
                          key={dropdownItem.name}
                          to={dropdownItem.href}
                          className="block px-4 py-2 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-sm"
                          onClick={() => setMobileMenuOpen(false)}
                        >
                          {dropdownItem.name}
                        </Link>
                      ))}
                    </div>
                  </div>
                ) : (
                  <Link
                    to={item.href}
                    className="block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}

            {isLoggedIn && (
              <>
                {menuItems.map((item) => (
                  <Link
                    key={item.path}
                    to={item.path}
                    className="flex items-center px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base"
                    onClick={() => setMobileMenuOpen(false)}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2 text-[#2D8C88]"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d={item.icon}
                      />
                    </svg>
                    {item.label}
                  </Link>
                ))}
                <button
                  onClick={() => { localStorage.clear(); window.location.href = '/login'; }}
                  className="flex items-center w-full px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-[#F28C38]"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"
                    />
                  </svg>
                  Logout
                </button>
              </>
            )}

            {!isLoggedIn && (
              <>
                <Link
                  to="/login"
                  className="block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Login
                </Link>
                <Link
                  to="/schedule-demo"
                  className="block px-4 py-3 text-[#1F2937] font-sans font-medium hover:bg-[#F28C38]/10 hover:text-[#F28C38] rounded-lg transition-colors duration-200 text-base"
                  onClick={() => setMobileMenuOpen(false)}
                >
                  Schedule Demo
                </Link>
              </>
            )}
          </motion.div>
        )}
      </div>
    </nav>
  );
};

export default Navbar;