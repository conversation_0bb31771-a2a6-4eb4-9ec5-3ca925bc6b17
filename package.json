{"name": "via-tryon-frontend", "version": "0.1.0", "private": true, "dependencies": {"axios": "^1.9.0", "date-fns": "^2.30.0", "framer-motion": "^11.11.17", "lucide-react": "^0.294.0", "qrcode.react": "^4.2.0", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hot-toast": "^2.4.1", "react-router-dom": "^6.28.0", "react-scripts": "5.0.1", "react-toastify": "^11.0.5", "recharts": "^2.8.0", "web-vitals": "^4.2.4"}, "scripts": {"start": "react-scripts start", "build": "react-scripts build", "test": "react-scripts test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"tailwindcss": "^3.4.17"}, "overrides": {"nth-check": "^2.1.1", "postcss": "^8.4.31", "svgo": "^3.0.0"}}