import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { useLocation } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';

const SearchResults = () => {
  const location = useLocation();
  const query = new URLSearchParams(location.search).get('q') || '';
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Reset state when query changes
    setLoading(true);
    
    // Simulate API call to fetch search results
    const fetchResults = async () => {
      // In a real app, this would be an API call
      // For demo purposes, we'll create mock data
      setTimeout(() => {
        const mockResults = [
          {
            id: 1,
            name: 'Classic Gold Watch',
            category: 'watches',
            price: 299.99,
            image: '/imgs/tryon.png'
          },
          {
            id: 2,
            name: 'Silver Bracelet',
            category: 'bracelets',
            price: 149.99,
            image: '/imgs/fitsize.png'
          },
          {
            id: 3,
            name: 'Diamond Watch',
            category: 'watches',
            price: 599.99,
            image: '/imgs/tryon-3.png'
          },
          {
            id: 4,
            name: 'Charm Bracelet',
            category: 'bracelets',
            price: 199.99,
            image: '/imgs/tryon-2.png'
          }
        ].filter(item => 
          item.name.toLowerCase().includes(query.toLowerCase()) || 
          item.category.toLowerCase().includes(query.toLowerCase())
        );
        
        setResults(mockResults);
        setLoading(false);
      }, 1000);
    };

    if (query) {
      fetchResults();
    } else {
      setResults([]);
      setLoading(false);
    }
  }, [query]);

  return (
    <div className="min-h-screen bg-[#F9FAFB] overflow-x-hidden">
      <Navbar />

      <section className="pt-36 pb-24 md:pt-40 md:pb-32">
        <div className="container mx-auto px-6">
          <motion.div 
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            className="text-center mb-12"
          >
            <h1 className="text-3xl md:text-4xl font-serif text-[#1F2937] mb-4">
              {query ? `Search Results for "${query}"` : 'Search Results'}
            </h1>
            <div className="w-20 h-px mx-auto mb-6" style={{ backgroundColor: '#F28C38' }}></div>
            <p className="text-[#1F2937] max-w-2xl mx-auto font-sans">
              {loading ? 'Searching...' : 
                results.length > 0 
                  ? `Found ${results.length} result${results.length === 1 ? '' : 's'} for your search.`
                  : 'No results found. Try a different search term.'}
            </p>
          </motion.div>

          {loading ? (
            <div className="flex justify-center items-center py-20">
              <div className="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-[#2D8C88]"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
              {results.map((item, index) => (
                <motion.div
                  key={item.id}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  className="bg-white rounded-xl shadow-md overflow-hidden"
                >
                  <div className="relative h-48 bg-gray-200">
                    <img 
                      src={item.image} 
                      alt={item.name} 
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <div className="p-6">
                    <div className="uppercase tracking-wide text-xs text-[#2D8C88] font-semibold">
                      {item.category}
                    </div>
                    <h3 className="mt-1 text-lg font-serif text-[#1F2937] font-medium">
                      {item.name}
                    </h3>
                    <p className="mt-2 text-[#F28C38] font-sans font-medium">
                      ${item.price.toFixed(2)}
                    </p>
                    <button
                      className="mt-4 w-full bg-[#2D8C88] text-white px-4 py-2 rounded-full font-sans font-medium text-sm shadow-sm hover:shadow-md transition-all duration-200"
                      onMouseEnter={(e) => (e.currentTarget.style.backgroundColor = '#F28C38')}
                      onMouseLeave={(e) => (e.currentTarget.style.backgroundColor = '#2D8C88')}
                    >
                      Try On Virtually
                    </button>
                  </div>
                </motion.div>
              ))}
            </div>
          )}

          {!loading && results.length === 0 && query && (
            <div className="text-center py-16">
              <svg 
                xmlns="http://www.w3.org/2000/svg" 
                className="h-16 w-16 mx-auto text-gray-400 mb-6" 
                fill="none" 
                viewBox="0 0 24 24" 
                stroke="currentColor"
              >
                <path 
                  strokeLinecap="round" 
                  strokeLinejoin="round" 
                  strokeWidth={1.5} 
                  d="M9.172 16.172a4 4 0 015.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" 
                />
              </svg>
              <h2 className="text-2xl font-serif text-[#1F2937] mb-4">No results found</h2>
              <p className="text-gray-600 font-sans max-w-md mx-auto">
                We couldn't find any products matching your search. Try using different keywords or browse our collections.
              </p>
            </div>
          )}
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default SearchResults;
