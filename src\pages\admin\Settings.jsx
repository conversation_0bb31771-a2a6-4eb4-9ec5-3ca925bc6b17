import { useState, useEffect } from 'react';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminNavbar from '../../components/admin/AdminNavbar';

const Settings = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [message, setMessage] = useState({ type: '', text: '' });
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    settings: {
      notifications: {
        email: true,
        sms: false
      },
      analytics: {
        dataRetention: 365,
        shareData: false
      }
    }
  });

  // Fetch admin data from backend
  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');
        if (!token) return;

        const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/me`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        });

        if (response.ok) {
          const userData = await response.json();
          setFormData({
            name: userData.name || userData.contactName || '',
            email: userData.email || '',
            phone: userData.phone || '',
            settings: {
              notifications: {
                email: userData.settings?.notifications?.email ?? true,
                sms: userData.settings?.notifications?.sms ?? false
              },
              analytics: {
                dataRetention: userData.settings?.analytics?.dataRetention || 365,
                shareData: userData.settings?.analytics?.shareData ?? false
              }
            }
          });
        }
      } catch (error) {
        console.error('Error fetching admin data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, []);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  const handleInputChange = (e) => {
    const { name, value, type, checked } = e.target;

    // Handle nested settings
    if (name.includes('.')) {
      const [parent, child, grandchild] = name.split('.');
      setFormData(prev => ({
        ...prev,
        [parent]: {
          ...prev[parent],
          [child]: grandchild ? {
            ...prev[parent][child],
            [grandchild]: type === 'checkbox' ? checked : value
          } : (type === 'checkbox' ? checked : value)
        }
      }));
    } else {
      setFormData(prev => ({
        ...prev,
        [name]: type === 'checkbox' ? checked : value
      }));
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      setSaving(true);
      setMessage({ type: '', text: '' });

      const token = localStorage.getItem('token');
      if (!token) {
        setMessage({ type: 'error', text: 'No authentication token found' });
        return;
      }

      const response = await fetch(`${process.env.REACT_APP_API_URL}/api/auth/update-profile`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(formData)
      });

      if (response.ok) {
        setMessage({ type: 'success', text: 'Settings updated successfully!' });
        // Auto-hide success message after 3 seconds
        setTimeout(() => setMessage({ type: '', text: '' }), 3000);
      } else {
        const errorData = await response.json();
        setMessage({ type: 'error', text: errorData.message || 'Failed to update settings' });
      }
    } catch (error) {
      console.error('Error updating profile:', error);
      setMessage({ type: 'error', text: 'Network error. Please try again.' });
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-20 transition-all duration-300`}>
        <div className="p-4 md:p-6 space-y-6">
          {/* Page Header */}
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admin Settings</h1>
            <p className="text-gray-600">Manage your admin account settings and platform preferences</p>
          </div>

          {/* Settings Content */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="border-b border-gray-200 px-4 md:px-6 py-4">
              <h2 className="text-lg font-medium text-gray-900">Admin Profile</h2>
              <p className="text-sm text-gray-500">Update your admin account information</p>
            </div>

            <div className="p-4 md:p-6">
              {/* Message Display */}
              {message.text && (
                <div className={`mb-4 p-4 rounded-md ${
                  message.type === 'success'
                    ? 'bg-green-50 border border-green-200 text-green-800'
                    : 'bg-red-50 border border-red-200 text-red-800'
                }`}>
                  {message.text}
                </div>
              )}

              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88]"></div>
                </div>
              ) : (
                <form onSubmit={handleSubmit}>
                  <div className="space-y-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <label htmlFor="name" className="block text-sm font-medium text-gray-700">Admin Name</label>
                        <input
                          type="text"
                          id="name"
                          name="name"
                          value={formData.name}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm"
                          placeholder="Enter admin name"
                        />
                      </div>
                      <div>
                        <label htmlFor="email" className="block text-sm font-medium text-gray-700">Email Address</label>
                        <input
                          type="email"
                          id="email"
                          name="email"
                          value={formData.email}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm"
                          placeholder="Enter admin email address"
                        />
                      </div>
                      <div>
                        <label htmlFor="phone" className="block text-sm font-medium text-gray-700">Phone Number</label>
                        <input
                          type="tel"
                          id="phone"
                          name="phone"
                          value={formData.phone}
                          onChange={handleInputChange}
                          className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm"
                          placeholder="Enter phone number"
                        />
                      </div>
                    </div>

                    {/* Notification Settings */}
                    <div className="mt-8">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Notification Preferences</h3>
                      <div className="space-y-4">
                        <div className="flex items-center">
                          <input
                            id="emailNotifications"
                            name="settings.notifications.email"
                            type="checkbox"
                            checked={formData.settings.notifications.email}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-[#2D8C88] focus:ring-[#2D8C88] border-gray-300 rounded"
                          />
                          <label htmlFor="emailNotifications" className="ml-2 block text-sm text-gray-900">
                            Email notifications
                          </label>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="smsNotifications"
                            name="settings.notifications.sms"
                            type="checkbox"
                            checked={formData.settings.notifications.sms}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-[#2D8C88] focus:ring-[#2D8C88] border-gray-300 rounded"
                          />
                          <label htmlFor="smsNotifications" className="ml-2 block text-sm text-gray-900">
                            SMS notifications
                          </label>
                        </div>
                      </div>
                    </div>

                    {/* Platform Settings */}
                    <div className="mt-8">
                      <h3 className="text-lg font-medium text-gray-900 mb-4">Platform Configuration</h3>
                      <div className="space-y-4">
                        <div>
                          <label htmlFor="dataRetention" className="block text-sm font-medium text-gray-700">Data Retention (days)</label>
                          <input
                            type="number"
                            id="dataRetention"
                            name="settings.analytics.dataRetention"
                            value={formData.settings.analytics.dataRetention}
                            onChange={handleInputChange}
                            min="30"
                            max="3650"
                            className="mt-1 block w-full rounded-md border-gray-300 shadow-sm focus:border-[#2D8C88] focus:ring-[#2D8C88] sm:text-sm"
                            placeholder="365"
                          />
                          <p className="mt-1 text-sm text-gray-500">How long to keep analytics data (30-3650 days)</p>
                        </div>
                        <div className="flex items-center">
                          <input
                            id="shareData"
                            name="settings.analytics.shareData"
                            type="checkbox"
                            checked={formData.settings.analytics.shareData}
                            onChange={handleInputChange}
                            className="h-4 w-4 text-[#2D8C88] focus:ring-[#2D8C88] border-gray-300 rounded"
                          />
                          <label htmlFor="shareData" className="ml-2 block text-sm text-gray-900">
                            Share anonymized platform data for improvements
                          </label>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Save Button */}
                  <div className="mt-6 flex justify-end">
                    <button
                      type="submit"
                      disabled={saving}
                      className="inline-flex justify-center rounded-md border border-transparent bg-[#2D8C88] px-4 py-2 text-sm font-medium text-white shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed"
                    >
                      {saving ? 'Saving...' : 'Save Changes'}
                    </button>
                  </div>
                </form>
              )}
            </div>
          </div>
        </div>
      </main>
    </div>
  );
};

export default Settings; 