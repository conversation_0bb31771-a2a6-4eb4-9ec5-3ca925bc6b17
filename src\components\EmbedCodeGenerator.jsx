import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Copy, Check, Code, Globe, Smartphone, Monitor } from 'lucide-react';

const EmbedCodeGenerator = ({ isOpen, onClose, clientData }) => {
  const [copied, setCopied] = useState(false);
  const [selectedProduct, setSelectedProduct] = useState('');
  const [productName, setProductName] = useState(clientData?.productName || '');
  const [buttonStyle, setButtonStyle] = useState('default');
  const [buttonSize, setButtonSize] = useState('medium');
  const [caseDimensions, setCaseDimensions] = useState(
    clientData?.productType === 'bracelets' ? '15' : '42'
  );
  const [productType, setProductType] = useState(clientData?.productType || 'watches');

  // Update productType and dimensions when clientData changes
  useEffect(() => {
    if (clientData?.productType) {
      setProductType(clientData.productType);
      setCaseDimensions(clientData.productType === 'bracelets' ? '15' : '42');
    }
  }, [clientData?.productType]);

  // Update dimensions when product type changes
  useEffect(() => {
    setCaseDimensions(productType === 'bracelets' ? '15' : '42');
  }, [productType]);

  const generateEmbedCode = () => {
    // Automatically use the logged-in client's ID from the database (MongoDB ObjectId without 'object' prefix)
    const clientId = clientData?.id || clientData?._id || 'AUTO_CLIENT_ID';
    const productImageUrl = selectedProduct || clientData?.productImageUrl || 'YOUR_PRODUCT_IMAGE_URL';
    const caseSize = caseDimensions || clientData?.caseDimensions || '42'; // Use state value
    const websiteUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';
    const finalProductName = productName || clientData?.productName || 'Product Name';

    return `<!-- ViaTryon Virtual Try-On Button for ${finalProductName} -->
<!-- Client ID: ${clientId} | Product Type: ${productType} | Size: ${caseSize}mm -->
<button
  onclick="openViaTryon('${productImageUrl}', '${clientId}', '${caseSize}', '${productType}', '${finalProductName}')"
  class="viatryon-btn viatryon-btn-${buttonStyle} viatryon-btn-${buttonSize}"
  style="
    background-color: ${buttonStyle === 'primary' ? '#2D8C88' : buttonStyle === 'outline' ? 'transparent' : '#333'};
    color: ${buttonStyle === 'outline' ? '#2D8C88' : 'white'};
    border: ${buttonStyle === 'outline' ? '2px solid #2D8C88' : 'none'};
    padding: ${buttonSize === 'small' ? '8px 16px' : buttonSize === 'large' ? '16px 32px' : '12px 24px'};
    font-size: ${buttonSize === 'small' ? '14px' : buttonSize === 'large' ? '18px' : '16px'};
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
  "
  onmouseover="this.style.opacity='0.8'"
  onmouseout="this.style.opacity='1'"
>
  Try On Virtually
</button>

<script>
function openViaTryon(imageUrl, clientId, caseDimensions, productType, productName) {
  // Construct the ViaTryon URL with parameters including product name
  const tryonUrl = '${websiteUrl}/tryon?' +
    'image=' + encodeURIComponent(imageUrl) +
    '&client=' + encodeURIComponent(clientId) +
    '&size=' + encodeURIComponent(caseDimensions) +
    '&type=' + encodeURIComponent(productType || 'watches') +
    '&name=' + encodeURIComponent(productName || 'Product');

  // Open in new window/tab
  window.open(tryonUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');
}
</script>

<!-- Usage Instructions:
1. Replace 'YOUR_PRODUCT_IMAGE_URL' with the actual URL of your product image
2. Update caseDimensions with the actual case size (e.g., '42' for 42mm)
3. Make sure the product image has a white background for best results
-->`;
  };

  const copyToClipboard = () => {
    navigator.clipboard.writeText(generateEmbedCode());
    setCopied(true);
    setTimeout(() => setCopied(false), 2000);
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
      >
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center justify-between">
            <div>
              <h2 className="text-xl font-semibold text-gray-900">Virtual Try-On Integration</h2>
              <p className="text-gray-600">Generate embed code for {clientData?.companyName || 'your website'}</p>
              {clientData?.id && (
                <p className="text-sm text-gray-500 mt-1">Client ID: {clientData.id}</p>
              )}
              {clientData?.productName && (
                <p className="text-sm text-gray-500 mt-1">Product: {clientData.productName}</p>
              )}
            </div>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <svg className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
              </svg>
            </button>
          </div>
        </div>

        <div className="p-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            {/* Configuration Panel */}
            <div className="space-y-6">
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration</h3>
                
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Product Image URL
                    </label>
                    <input
                      type="text"
                      value={selectedProduct}
                      onChange={(e) => setSelectedProduct(e.target.value)}
                      placeholder={clientData?.productImageUrl || "https://yoursite.com/images/watch.png"}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      URL of the product image with transparent background
                      {clientData?.productImageUrl && (
                        <span className="block text-green-600 mt-1">Default: {clientData.productImageUrl}</span>
                      )}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Product Name
                    </label>
                    <input
                      type="text"
                      value={productName}
                      onChange={(e) => setProductName(e.target.value)}
                      placeholder={clientData?.productName || "Product Name"}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      Name of the product for analytics tracking
                      {clientData?.productName && (
                        <span className="block text-green-600 mt-1">Default: {clientData.productName}</span>
                      )}
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Product Type
                    </label>
                    <select
                      value={productType}
                      onChange={(e) => setProductType(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    >
                      <option value="watches">Watches</option>
                      <option value="bracelets">Bracelets</option>
                    </select>
                    <p className="text-xs text-gray-500 mt-1">
                      Select the type of product for optimal try-on experience
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      {productType === 'watches' ? 'Case Dimensions (mm)' : 'Width (mm)'}
                    </label>
                    <input
                      type="number"
                      value={caseDimensions}
                      onChange={(e) => setCaseDimensions(e.target.value)}
                      placeholder={productType === 'watches' ? '42' : '15'}
                      min={productType === 'watches' ? '20' : '10'}
                      max={productType === 'watches' ? '60' : '30'}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    />
                    <p className="text-xs text-gray-500 mt-1">
                      {productType === 'watches'
                        ? 'Watch case diameter for proper scaling (20-60mm)'
                        : 'Bracelet width for proper scaling (10-30mm)'
                      }
                    </p>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Style
                    </label>
                    <select
                      value={buttonStyle}
                      onChange={(e) => setButtonStyle(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    >
                      <option value="default">Default</option>
                      <option value="primary">Primary</option>
                      <option value="outline">Outline</option>
                      <option value="minimal">Minimal</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Button Size
                    </label>
                    <select
                      value={buttonSize}
                      onChange={(e) => setButtonSize(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                    >
                      <option value="small">Small</option>
                      <option value="medium">Medium</option>
                      <option value="large">Large</option>
                    </select>
                  </div>
                </div>
              </div>

              {/* Preview */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                  <div className="flex items-center justify-center">
                    <button
                      className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                        buttonStyle === 'primary' ? 'bg-[#2D8C88] text-white hover:bg-[#236b68]' :
                        buttonStyle === 'outline' ? 'border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white' :
                        buttonStyle === 'minimal' ? 'text-[#2D8C88] hover:bg-[#2D8C88]/10' :
                        'bg-gray-800 text-white hover:bg-gray-700'
                      } ${
                        buttonSize === 'small' ? 'text-sm px-3 py-1.5' :
                        buttonSize === 'large' ? 'text-lg px-6 py-3' :
                        'text-base px-4 py-2'
                      }`}
                    >
                      <Globe className="h-4 w-4 mr-2" />
                      Try On Virtually
                    </button>
                  </div>
                </div>
              </div>

              {/* Device Compatibility */}
              <div>
                <h3 className="text-lg font-medium text-gray-900 mb-4">Device Compatibility</h3>
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Smartphone className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">Mobile</p>
                    <p className="text-xs text-green-600">Optimized</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Monitor className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">Desktop</p>
                    <p className="text-xs text-green-600">Supported</p>
                  </div>
                  <div className="text-center p-3 bg-green-50 rounded-lg">
                    <Globe className="h-6 w-6 text-green-600 mx-auto mb-2" />
                    <p className="text-sm font-medium text-green-800">Tablet</p>
                    <p className="text-xs text-green-600">Supported</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Code Panel */}
            <div>
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-medium text-gray-900">Embed Code</h3>
                <button
                  onClick={copyToClipboard}
                  className="inline-flex items-center px-3 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors"
                >
                  {copied ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                  {copied ? 'Copied!' : 'Copy Code'}
                </button>
              </div>
              
              <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                <pre className="text-green-400 text-sm">
                  <code>{generateEmbedCode()}</code>
                </pre>
              </div>

              <div className="mt-6 space-y-4">
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 mb-2">Integration Steps:</h4>
                  <ol className="text-sm text-blue-800 space-y-1">
                    <li>1. Copy the embed code above</li>
                    <li>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URL</li>
                    <li>3. Update the dimensions (size) for each product</li>
                    <li>4. Your client ID is automatically included for analytics tracking</li>
                    <li>5. Ensure the product type matches your product category</li>
                    <li>6. Paste the code into your product page HTML</li>
                    <li>7. Test the integration on your website</li>
                  </ol>
                  <div className="mt-3 p-3 bg-blue-100 rounded-lg">
                    <p className="text-xs text-blue-700">
                      <strong>Note:</strong> Your client ID ({clientData?.id || 'YOUR_CLIENT_ID'}) is automatically included in the code for analytics tracking.
                    </p>
                  </div>
                </div>

                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                  <h4 className="font-medium text-yellow-900 mb-2">Need Help?</h4>
                  <p className="text-sm text-yellow-800">
                    Check our <a href="#" className="underline">integration guide</a> or 
                    <a href="#" className="underline ml-1">contact support</a> for assistance.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </motion.div>
    </div>
  );
};

export default EmbedCodeGenerator;
