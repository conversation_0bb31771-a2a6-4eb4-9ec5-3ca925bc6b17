import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Link } from 'react-router-dom';
import Navbar from '../components/Navbar';
import Footer from '../components/Footer';
import { getProductCollections } from '../data/productCollections';
import { removeProductBackground } from '../utils/backgroundRemover';

const Home = () => {
  const [activeCategory, setActiveCategory] = useState('watches');
  const [tryOnActive, setTryOnActive] = useState(false);
  const [hoveredProduct, setHoveredProduct] = useState(null);
  const [currentHeroIndex, setCurrentHeroIndex] = useState(0);
  const [activeSolution, setActiveSolution] = useState('virtual-try-on');
  const [featuredProducts, setFeaturedProducts] = useState({ watches: [], bracelets: [], rings: [], earrings: [] });
  const [heroProducts, setHeroProducts] = useState([
    { type: 'watch', image: '/imgs/watches_hero/watch_1_hero.png' },
    { type: 'bracelet', image: '/imgs/bracelets_hero/bracelet_1_hero.png' },
    { type: 'ring', image: '/imgs/ring.png' },
    { type: 'earring', image: '/imgs/earring.png' },
  ]);
  const [loading, setLoading] = useState(true);

  // Process hero images with background removal
  const processHeroImage = async (imageSrc, productType) => {
    try {
      if (productType === 'ring' || productType === 'earring') {
        return await removeProductBackground(imageSrc, productType);
      }
      return imageSrc; // Return original for watches and bracelets (they already have backgrounds removed)
    } catch (error) {
      console.warn(`Failed to process ${productType} image:`, error);
      return imageSrc; // Return original if processing fails
    }
  };

  // Load featured products and hero products
  useEffect(() => {
    const loadFeaturedProducts = async () => {
      try {
        const collections = await getProductCollections();
        setFeaturedProducts({
          watches: collections.watches.slice(0, 3),
          bracelets: collections.bracelets.slice(0, 3),
          rings: collections.rings.slice(0, 3),
          earrings: collections.earrings.slice(0, 3)
        });

        // Process ring and earring images with background removal
        const processedRingImage = await processHeroImage('/imgs/ring.png', 'ring');
        const processedEarringImage = await processHeroImage('/imgs/earring.png', 'earring');

        // Always use the correct hero images, regardless of product collections
        setHeroProducts([
          { type: 'watch', image: collections.watches[0]?.image || '/imgs/watches_hero/watch_1_hero.png' },
          { type: 'bracelet', image: collections.bracelets[0]?.image || '/imgs/bracelets_hero/bracelet_1_hero.png' },
          { type: 'ring', image: processedRingImage },
          { type: 'earring', image: processedEarringImage },
        ]);
      } catch (error) {
        console.error('Error loading featured products:', error);
        // Fallback to default images if loading fails
        setHeroProducts([
          { type: 'watch', image: '/imgs/watches_hero/watch_1_hero.png' },
          { type: 'bracelet', image: '/imgs/bracelets_hero/bracelet_1_hero.png' },
          { type: 'ring', image: '/imgs/ring.png' },
          { type: 'earring', image: '/imgs/earring.png' },
        ]);
      } finally {
        setLoading(false);
      }
    };

    loadFeaturedProducts();
  }, []);

  // Auto-rotate hero products
  useEffect(() => {
    const interval = setInterval(() => {
      if (!hoveredProduct) {
        setCurrentHeroIndex((prev) => (prev + 1) % heroProducts.length);
      }
    }, 5000);
    return () => clearInterval(interval);
  }, [hoveredProduct, heroProducts.length]);

  // Brand logos with real links
  const brandLogos = [
    { src: '/imgs/brands/Rolex.png', url: 'https://www.rolex.com', alt: 'Rolex' },
    { src: 'https://www.fossil.com/on/demandware.static/Sites-fossil-gl-Site/-/default/dw9c7e7989/images/FSL-2023-NEW-Logo.svg', url: 'https://www.fossil.com', alt: 'Fossil' },
    { src: 'https://logolook.net/wp-content/uploads/2022/09/Seiko-Logo.png', url: 'https://www.seikowatches.com', alt: 'Seiko' },
    { src: 'https://th.bing.com/th/id/OIP.zz_4M3PAuV3lILlJohWsYAHaDI?rs=1&pid=ImgDetMain', url: 'https://www.danielwellington.com', alt: 'Daniel Wellington' },
    { src: '/imgs/brands/Citizen.png', url: 'https://www.citizenwatch-global.com/', alt: 'Citizen' },
    { src: '/imgs/brands/Omega.png', url: 'https://www.omegawatches.com', alt: 'Omega' },
    { src: '/imgs/brands/Tissot.png', url: 'https://www.tissotwatches.com', alt: 'Tissot' },
  ];

  // Solutions data
  const solutions = {
    'virtual-try-on': {
      title: 'Virtual Try-On',
      desc: 'Experience jewelry and watches in real-time with our web-based AR technology. No apps or uploads needed.',
      image: '/imgs/tryon.png',
    },
    'visualize-fit-size': {
      title: 'Visualize Fit & Size',
      desc: 'Ensure the perfect fit with accurate sizing and proportion previews tailored to your wrist.',
      image: '/imgs/fitsize.png',
    },
    'build-your-look': {
      title: 'Build Your Look',
      desc: 'Mix and match accessories to create your ideal style, visualized instantly in AR.',
      image: '/imgs/tryon-3.png',
    },
    'rings-earrings': {
      title: 'Rings & Earrings',
      desc: 'Perfect your jewelry selection with precise AR visualization for rings and earrings on your hands and ears.',
      image: '/imgs/rings01.jpg',
    },
  };

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50 overflow-x-hidden">
      <Navbar />

      {/* Hero Section */}
      <section className="relative min-h-[100vh] md:min-h-[90vh] flex items-center pt-24 pb-12 md:pt-32 md:pb-32 overflow-hidden">
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1.5, ease: "easeOut" }}
            className="absolute top-1/4 -left-32 md:-left-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]"
            style={{
              background: `linear-gradient(135deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,
            }}
          />
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 1.5, delay: 0.3, ease: "easeOut" }}
            className="absolute bottom-1/4 -right-32 md:-right-60 w-[20rem] md:w-[40rem] h-[20rem] md:h-[40rem] rounded-full blur-[80px] md:blur-[120px]"
            style={{
              background: `linear-gradient(315deg, rgba(45, 140, 136, 0.15) 0%, rgba(242, 140, 56, 0.15) 100%)`,
            }}
          />
        </div>

        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="flex flex-col lg:flex-row items-center justify-between gap-8 lg:gap-16">
            {/* Hero Content */}
            <motion.div 
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.8, ease: "easeOut" }}
              className="w-full lg:w-1/2 text-center lg:text-left order-1 lg:order-1"
            >
              <div className="relative inline-block mb-6">
                <span className="text-sm md:text-base font-medium px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88]">
                  Experience the Future of Shopping
                </span>
              </div>
              <h1 className="text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-serif text-gray-900 mb-6 md:mb-8 leading-tight">
                <span className="block italic font-light bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent">Experience</span>
                <span className="block font-medium mt-2 bg-gradient-to-r from-[#2D8C88] to-[#F28C38] bg-clip-text text-transparent">
                  Virtual Try-On
                </span>
              </h1>
              <p className="text-lg md:text-xl text-gray-600 mb-8 md:mb-10 max-w-md mx-auto lg:mx-0 font-sans leading-relaxed">
              Empower your customers to truly connect with every piece. Our cutting-edge, browser-based AR technology enables instant, realistic virtual try-ons for watches, bracelets, rings, and earrings, eliminating guesswork and boosting purchase confidence.              </p>

              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4 justify-center lg:justify-start">
                <Link
                  to="/virtual-try-on"
                  className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-white rounded-full overflow-hidden transition-all duration-300 ease-out hover:scale-105"
                  style={{ backgroundColor: '#2D8C88' }}
                >
                  <span className="absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100"></span>
                  <span className="relative flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z" />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Try It Now
                  </span>
                </Link>
                <Link to="/virtual-try-on">
                  <button
                    className="group relative inline-flex items-center justify-center px-8 py-4 text-lg font-medium text-gray-900 bg-white rounded-full border border-gray-200 transition-all duration-300 ease-out hover:scale-105 hover:border-[#F28C38] hover:text-[#F28C38]"
                  >
                    <span className="relative flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 6h16M4 12h16M4 18h16" />
                      </svg>
                      Browse Collection
                    </span>
                  </button>
                </Link>
              </div>

              {/* Hero Indicators */}
              <div className="flex items-center justify-center lg:justify-start space-x-3 mt-8">
                {heroProducts.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentHeroIndex(index)}
                    className={`transition-all duration-300 ease-out ${
                      currentHeroIndex === index ? 'w-8 bg-[#F28C38]' : 'w-3 bg-gray-300'
                    } h-2 rounded-full`}
                  />
                ))}
              </div>
            </motion.div>

            {/* Hero Product Display - Mobile Optimized */}
            <motion.div 
              initial={{ opacity: 0, scale: 0.9 }}
              animate={{ opacity: 1, scale: 1 }}
              transition={{ duration: 0.8, delay: 0.2, ease: "easeOut" }}
              className="w-full lg:w-1/2 relative order-2 lg:order-2 mb-8 lg:mb-0"
            >
              <div className="relative h-[20rem] sm:h-[24rem] md:h-[32rem] lg:h-[40rem] flex items-center justify-center">
                {/* Mobile-specific background */}
                <div className="lg:hidden absolute inset-0 flex items-center justify-center">
                  <div className="w-[18rem] h-[18rem] sm:w-[22rem] sm:h-[22rem] rounded-full bg-gradient-to-br from-[#2D8C88]/10 to-[#F28C38]/10 shadow-lg border border-white/20 backdrop-blur-sm"></div>
                </div>
                
                {/* Desktop background */}
                <div className="hidden lg:block absolute w-[44rem] h-[44rem] rounded-full shadow-inner border"
                  style={{
                    background: `linear-gradient(135deg, rgba(45, 140, 136, 0.1) 0%, rgba(242, 140, 56, 0.1) 100%)`,
                    borderColor: 'rgba(242, 140, 56, 0.2)',
                  }}
                />

                <AnimatePresence mode="wait">
                  {heroProducts.length > 0 && heroProducts.map((product, index) => (
                    <motion.div
                      key={`${product.type}-${index}`}
                      initial={{ opacity: 0, scale: 0.8, y: 20 }}
                      animate={{
                        opacity: currentHeroIndex === index ? 1 : 0,
                        scale: currentHeroIndex === index ? 1 : 0.8,
                        y: currentHeroIndex === index ? 0 : 20
                      }}
                      exit={{ opacity: 0, scale: 0.8, y: -20 }}
                      transition={{ duration: 0.6, ease: "easeInOut" }}
                      className={`absolute inset-0 flex items-center justify-center ${index === currentHeroIndex ? 'z-10' : 'z-0'}`}
                    >
                      <div className="relative flex items-center justify-center w-full h-full">
                        <img
                          src={product.image}
                          alt={`${product.type} - Virtual Try-On`}
                          className="h-[16rem] sm:h-[20rem] md:h-[28rem] lg:h-[36rem] w-auto object-contain drop-shadow-[0_8px_25px_rgba(0,0,0,0.15)] max-w-full transform-gpu"
                          style={{
                            maxHeight: '100%',
                            width: 'auto',
                            objectFit: 'contain',
                            transform: 'translateZ(0)',
                            backfaceVisibility: 'hidden',
                            WebkitTransform: 'translateZ(0)',
                            WebkitBackfaceVisibility: 'hidden'
                          }}
                          onError={(e) => {
                            console.log(`Failed to load image: ${product.image}`);
                            if (product.type === 'watch') {
                              e.target.src = '/imgs/watches/watch_1.png';
                            } else if (product.type === 'bracelet') {
                              e.target.src = '/imgs/bracelets/bracelet_1.png';
                            } else if (product.type === 'ring') {
                              e.target.src = '/imgs/ring.png';
                            } else if (product.type === 'earring') {
                              e.target.src = '/imgs/earring.png';
                            }
                          }}
                          loading="eager"
                        />
                        <div
                          className="absolute -bottom-8 sm:-bottom-12 md:-bottom-16 left-1/2 transform -translate-x-1/2 w-2/3 sm:w-3/4 h-6 sm:h-8 md:h-12 rounded-full blur-xl md:blur-2xl"
                          style={{ backgroundColor: 'rgba(31, 41, 55, 0.15)' }}
                        />
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>

                {/* Loading State */}
                {loading && (
                  <div className="absolute inset-0 flex items-center justify-center">
                    <div className="animate-pulse">
                      <div className="w-32 h-32 sm:w-40 sm:h-40 md:w-48 md:h-48 bg-gray-200 rounded-full mx-auto mb-4"></div>
                      <div className="h-4 bg-gray-200 rounded w-24 mx-auto"></div>
                    </div>
                  </div>
                )}
              </div>
            </motion.div>
          </div>
        </div>
      </section>

      {/* Brands Section */}
      <section className="py-16 md:py-24 bg-[#F9FAFB] relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-5">
            <div className="absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]"></div>
            <div className="absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]"></div>
          </div>
        </div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-12 md:mb-16">
            <span className="inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4">
              Trusted by Leading Brands
            </span>
            <h2 className="text-2xl md:text-3xl font-serif text-[#1F2937] mb-4">Our Partners</h2>
            <div className="w-16 md:w-20 h-px mx-auto" style={{ backgroundColor: '#F28C38' }}></div>
          </div>
          <div className="relative h-16 md:h-20 overflow-hidden">
            <motion.div
              className="flex items-center"
              animate={{
                x: ['0%', '-50%'],
                transition: {
                  x: {
                    duration: 20,
                    repeat: Infinity,
                    ease: 'linear',
                  },
                },
              }}
            >
              {[...brandLogos, ...brandLogos].map((logo, index) => (
                <div key={index} className="flex-shrink-0 w-1/3 md:w-1/5 px-2 md:px-4">
                  <a 
                    href={logo.url} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="block p-4 bg-white rounded-xl shadow-sm hover:shadow-md transition-shadow duration-300"
                    aria-label={`Visit ${logo.alt}`}
                  >
                    <img
                      src={logo.src}
                      alt={logo.alt}
                      className="h-8 md:h-12 w-auto mx-auto opacity-70 hover:opacity-100 transition-opacity duration-200"
                    />
                  </a>
                </div>
              ))}
            </motion.div>
          </div>
        </div>
      </section>

      {/* Focused Category Section */}
      <section className="py-16 md:py-24 bg-gradient-to-b from-[#E5E7EB] to-white relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-5">
            <div className="absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]"></div>
            <div className="absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]"></div>
          </div>
        </div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-12 md:mb-16">
            <span className="inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4">
              Explore Our Collections
            </span>
            <h2 className="text-2xl md:text-3xl font-serif text-[#1F2937] mb-4">Featured Products</h2>
            <div className="w-16 md:w-20 h-px mx-auto mb-4 md:mb-6" style={{ backgroundColor: '#F28C38' }}></div>
            <p className="text-[#1F2937] max-w-2xl mx-auto font-sans text-sm md:text-base">
              Switch between watches, bracelets, rings, and earrings to find your perfect accessory.
            </p>
          </div>

          <div className="flex justify-center mb-8 md:mb-12">
            <div className="inline-flex rounded-lg shadow-sm bg-white border p-1 w-full max-w-lg md:w-auto" style={{ borderColor: '#E5E7EB' }}>
              <button
                onClick={() => setActiveCategory('watches')}
                className="flex-1 md:flex-none px-3 md:px-6 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-md transition-all duration-200 flex items-center justify-center whitespace-nowrap"
                style={{
                  backgroundColor: activeCategory === 'watches' ? '#2D8C88' : '#FFFFFF',
                  color: activeCategory === 'watches' ? '#FFFFFF' : '#1F2937',
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Watches
              </button>
              <button
                onClick={() => setActiveCategory('bracelets')}
                className="flex-1 md:flex-none px-3 md:px-6 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-md transition-all duration-200 flex items-center justify-center whitespace-nowrap"
                style={{
                  backgroundColor: activeCategory === 'bracelets' ? '#2D8C88' : '#FFFFFF',
                  color: activeCategory === 'bracelets' ? '#FFFFFF' : '#1F2937',
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
                Bracelets
              </button>
              <button
                onClick={() => setActiveCategory('rings')}
                className="flex-1 md:flex-none px-3 md:px-6 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-md transition-all duration-200 flex items-center justify-center whitespace-nowrap"
                style={{
                  backgroundColor: activeCategory === 'rings' ? '#2D8C88' : '#FFFFFF',
                  color: activeCategory === 'rings' ? '#FFFFFF' : '#1F2937',
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01" />
                </svg>
                Rings
              </button>
              <button
                onClick={() => setActiveCategory('earrings')}
                className="flex-1 md:flex-none px-3 md:px-6 py-2.5 md:py-3 text-xs md:text-sm font-sans font-medium rounded-md transition-all duration-200 flex items-center justify-center whitespace-nowrap"
                style={{
                  backgroundColor: activeCategory === 'earrings' ? '#2D8C88' : '#FFFFFF',
                  color: activeCategory === 'earrings' ? '#FFFFFF' : '#1F2937',
                }}
              >
                <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 md:h-4 md:w-4 mr-1 md:mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
                Earrings
              </button>
            </div>
          </div>

          {loading ? (
            <div className="flex items-center justify-center py-16 md:py-20">
              <div className="text-center">
                <div className="animate-spin rounded-full h-10 md:h-12 w-10 md:w-12 border-t-2 border-b-2 border-[#2D8C88] mx-auto mb-4"></div>
                <p className="text-[#1F2937] font-sans text-sm md:text-base">Loading featured products...</p>
              </div>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 md:gap-8">
              {featuredProducts[activeCategory].map((product, index) => (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  whileInView={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: index * 0.1 }}
                  viewport={{ once: true }}
                  className="group relative bg-white rounded-2xl shadow-sm hover:shadow-xl transition-all duration-300 border h-full flex flex-col"
                  style={{ borderColor: '#E5E7EB' }}
                  onMouseEnter={() => setHoveredProduct(index)}
                  onMouseLeave={() => setHoveredProduct(null)}
                >
                  <div className="relative h-56 md:h-72 bg-gradient-to-b from-[#F9FAFB] to-white flex items-center justify-center p-4 md:p-8 rounded-t-2xl">
                    <img
                      src={product.image}
                      alt={product.name}
                      className="h-40 md:h-56 w-auto object-contain transition-transform duration-300 group-hover:scale-105"
                    />
                    <Link
                      to={`/try-on/${activeCategory}/${index + 1}`}
                      className="absolute top-3 right-3 md:top-4 md:right-4 text-xs px-3 py-1.5 rounded-full font-sans font-medium shadow-sm cursor-pointer hover:bg-[#2D8C88] hover:text-white transition-colors"
                      style={{ backgroundColor: 'rgba(45, 140, 136, 0.1)', color: '#2D8C88' }}
                    >
                      Try It On
                    </Link>
                  </div>
                  <div className="p-4 md:p-6 flex-grow">
                    <div className="flex justify-between items-start mb-2">
                      <h3 className="text-base md:text-lg font-sans font-medium text-[#1F2937] flex-1 mr-2">{product.name}</h3>
                    </div>
                    <p className="text-[#1F2937] text-xs md:text-sm mb-4 font-sans opacity-75">
                      {activeCategory === 'watches' ? 'Crafted with precision and style' : 'Designed to complement your elegance'}
                    </p>
                    <div className="mt-auto">
                      <Link
                        to={`/try-on/${activeCategory}/${index + 1}`}
                        className="w-full bg-white border text-xs md:text-sm flex items-center justify-center px-4 md:px-6 py-2.5 md:py-3 rounded-full transition font-sans font-medium min-h-[44px] hover:bg-[#F28C38] hover:text-white hover:border-[#F28C38]"
                        style={{ borderColor: '#2D8C88', color: '#2D8C88' }}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-4 w-4 mr-2"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                          />
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                        Virtual Try-On
                      </Link>
                    </div>
                  </div>
                </motion.div>
              ))}
            </div>
          )}
        </div>
      </section>

      {/* Virtual Try-On Section */}
      <section
        className="py-20 md:py-32 relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%)`,
        }}
      >
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-5">
            <div className="absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]"></div>
            <div className="absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]"></div>
          </div>
        </div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4">
              Experience AR Technology
            </span>
            <h2 className="text-3xl md:text-4xl font-serif text-[#1F2937] mb-4">Virtual Try-On Experience</h2>
            <div className="w-20 h-px mx-auto mb-6" style={{ backgroundColor: '#F28C38' }}></div>
            <p className="text-[#1F2937] max-w-2xl mx-auto font-sans">
              See how our augmented reality technology brings watches and bracelets to life on your wrist.
            </p>
          </div>

          <div className="flex flex-col lg:flex-row items-center gap-12">
            <div className="lg:w-1/2">
              <div className="relative bg-white rounded-2xl shadow-lg overflow-hidden border" style={{ borderColor: '#E5E7EB' }}>
                <div className="aspect-[4/3] bg-[#1F2937] flex items-center justify-center relative">
                  {tryOnActive ? (
                    <div className="text-center p-8 text-white">
                      <div className="mb-8">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          className="h-16 w-16 mx-auto"
                          fill="none"
                          viewBox="0 0 24 24"
                          stroke="currentColor"
                          style={{ stroke: '#F28C38' }}
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth={2}
                            d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                          />
                        </svg>
                      </div>
                      <h3 className="text-2xl font-serif text-white mb-4">Camera Access Required</h3>
                      <p className="text-gray-300 mb-8 max-w-md mx-auto font-sans">
                        To begin your virtual try-on experience, please allow camera access when prompted.
                      </p>
                      <button
                        onClick={() => setTryOnActive(false)}
                        className="text-white px-6 py-3 rounded-full font-sans font-medium transition-colors duration-200"
                        style={{ backgroundColor: '#2D8C88' }}
                      >
                        Back to Preview
                      </button>
                    </div>
                  ) : (
                    <div className="relative h-full w-full">
                      <img
                        src="/try-on-demo.jpg"
                        alt="Virtual Try-On Demo"
                        className="absolute inset-0 w-full h-full object-cover opacity-80"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-[#1F2937]/80 via-[#1F2937]/30 to-[#1F2937]/80"></div>
                      <div className="relative z-10 h-full flex flex-col items-center justify-center p-8 text-center text-white">
                        <div className="mb-8">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-16 w-16 mx-auto"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                            style={{ stroke: '#F28C38' }}
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                            />
                            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                          </svg>
                        </div>
                        <h3 className="text-2xl font-serif text-white mb-4">See It On Your Wrist</h3>
                        <p className="text-gray-300 mb-8 max-w-md mx-auto font-sans">
                          Preview any watch or bracelet from our collection in real-time.
                        </p>
                        <Link
                          to="/virtual-try-on"
                          className="text-white px-8 py-4 rounded-full font-sans font-medium transition-colors duration-200 flex items-center"
                          style={{ backgroundColor: '#2D8C88' }}
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            className="h-5 w-5 mr-2"
                            fill="none"
                            viewBox="0 0 24 24"
                            stroke="currentColor"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2}
                              d="M15 10l4.553-2.276A1 1 0 0121 8.618v6.764a1 1 0 01-1.447.894L15 14M5 18h8a2 2 0 002-2V8a2 2 0 00-2-2H5a2 2 0 00-2 2v8a2 2 0 002 2z"
                            />
                          </svg>
                          Try It Now
                        </Link>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>

            <div className="lg:w-1/2">
              <h3 className="text-2xl font-serif text-[#1F2937] mb-6">Why Choose Our AR Try-On?</h3>
              <div className="space-y-6">
                {[
                  { title: 'Realistic Visualization', desc: 'Accurate sizing and proportions in real-time.' },
                  { title: 'Multi-Angle Views', desc: 'Explore every detail from any perspective.' },
                  { title: 'Device Compatibility', desc: 'Works seamlessly on smartphones and tablets.' },
                  { title: 'No App Required', desc: 'Instant access directly in your browser.' },
                ].map((feature, index) => (
                  <motion.div
                    key={index}
                    initial={{ opacity: 0, x: -20 }}
                    whileInView={{ opacity: 1, x: 0 }}
                    transition={{ duration: 0.5, delay: index * 0.1 }}
                    viewport={{ once: true }}
                    className="flex items-start"
                  >
                    <div className="p-2 rounded-full mr-4 flex-shrink-0" style={{ backgroundColor: 'rgba(242, 140, 56, 0.1)' }}>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                        style={{ stroke: '#F28C38' }}
                      >
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                      </svg>
                    </div>
                    <div>
                      <h4 className="text-[#1F2937] font-sans font-medium">{feature.title}</h4>
                      <p className="text-[#1F2937] font-sans opacity-75">{feature.desc}</p>
                    </div>
                  </motion.div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Solutions Section */}
      <section className="py-20 md:py-32 bg-[#F9FAFB] relative overflow-hidden">
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-5">
            <div className="absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#2D8C88]"></div>
            <div className="absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-[#F28C38]"></div>
          </div>
        </div>
        <div className="container mx-auto px-4 md:px-6 relative z-10">
          <div className="text-center mb-16">
            <span className="inline-block px-4 py-2 rounded-full bg-white/10 backdrop-blur-sm border border-white/20 text-[#2D8C88] text-sm md:text-base font-medium mb-4">
              Our Solutions
            </span>
            <h2 className="text-3xl md:text-4xl font-serif text-[#1F2937] mb-4">Elevate Your Experience</h2>
            <div className="w-20 h-px mx-auto mb-6" style={{ backgroundColor: '#F28C38' }}></div>
            <p className="text-[#1F2937] max-w-2xl mx-auto font-sans">
              Discover our suite of AR and 3D solutions designed to transform shopping.
            </p>
          </div>

          <div className="flex flex-col lg:flex-row gap-12">
            <div className="lg:w-1/2 flex flex-col justify-center">
              <div className="p-6">
                <h3 className="text-2xl font-serif text-[#1F2937] mb-4">{solutions[activeSolution].title}</h3>
                <p className="text-[#1F2937] leading-relaxed font-sans opacity-75">{solutions[activeSolution].desc}</p>
              </div>
              <div className="flex flex-wrap gap-4 justify-center lg:justify-start">
                {Object.keys(solutions).map((key) => (
                  <button
                    key={key}
                    onClick={() => setActiveSolution(key)}
                    className="px-6 py-3 text-sm font-sans font-medium transition-colors duration-200 relative"
                    style={{
                      color: activeSolution === key ? '#F28C38' : '#1F2937',
                    }}
                  >
                    {solutions[key].title}
                    <div
                      className="absolute bottom-0 left-0 h-0.5"
                      style={{
                        backgroundColor: '#F28C38',
                        width: activeSolution === key ? '100%' : '0%',
                        transition: 'width 0.3s',
                      }}
                    />
                  </button>
                ))}
              </div>
            </div>

            <div className="lg:w-1/2">
              <motion.div
                initial={{ opacity: 0, scale: 0.95 }}
                whileInView={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                viewport={{ once: true }}
                className="relative rounded-2xl overflow-hidden shadow-lg border"
                style={{ borderColor: '#E5E7EB' }}
              >
                <img
                  src={solutions[activeSolution].image}
                  alt={solutions[activeSolution].title}
                  className="w-full h-[28rem] object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-[#1F2937]/20 to-transparent"></div>
              </motion.div>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section
        className="py-20 md:py-32 text-white relative overflow-hidden"
        style={{
          background: `linear-gradient(135deg, #2D8C88 0%, #1F2937 50%, #2D8C88 100%)`,
        }}
      >
        {/* Background Elements */}
        <div className="absolute inset-0 overflow-hidden">
          <div className="absolute top-0 left-0 w-full h-full opacity-10">
            <div className="absolute top-1/4 -left-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white"></div>
            <div className="absolute bottom-1/4 -right-32 w-[20rem] h-[20rem] rounded-full blur-[80px] bg-white"></div>
          </div>
        </div>

        <div className="container mx-auto px-4 md:px-6 text-center relative z-10">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.8 }}
            viewport={{ once: true }}
          >
            <h2 className="text-3xl md:text-4xl lg:text-5xl font-serif text-white mb-6 leading-tight">
              Discover Your Perfect
              <span className="block mt-2 bg-gradient-to-r from-white to-[#F28C38] bg-clip-text text-transparent">
                Accessory Today
              </span>
            </h2>
            <p className="text-lg text-gray-100 mb-12 max-w-3xl mx-auto font-sans">
              Step into the future of shopping with our cutting-edge AR try-on. Visualize watches and bracelets on your wrist in real-time, anywhere, anytime.
            </p>
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-6">
              <Link to="/try-on/watches">
                <button
                  className="group relative w-full sm:w-auto bg-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg shadow-lg hover:shadow-xl transition-all duration-300 flex items-center justify-center"
                  style={{ color: '#2D8C88' }}
                >
                  <span className="absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-gradient-to-r from-[#F28C38] to-[#2D8C88] group-hover:opacity-100 rounded-full"></span>
                  <span className="relative flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 mr-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M14.752 11.168l-3.197-2.132A1 1 0 0010 9.87v4.263a1 1 0 001.555.832l3.197-2.132a1 1 0 000-1.664z"
                      />
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    Start Virtual Try-On
                  </span>
                </button>
              </Link>
              <Link to="/virtual-try-on">
                <button
                  className="group relative w-full sm:w-auto bg-transparent border-2 text-white px-8 md:px-12 py-4 md:py-5 rounded-full font-sans font-medium text-lg transition-all duration-300 flex items-center justify-center"
                  style={{ borderColor: '#F28C38' }}
                >
                  <span className="absolute inset-0 w-full h-full transition duration-300 ease-out opacity-0 bg-[#F28C38] group-hover:opacity-100 rounded-full"></span>
                  <span className="relative flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-6 w-6 mr-3"
                      fill="none"
                      viewBox="0 0 24 24"
                      stroke="currentColor"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M4 6h16M4 12h16M4 18h16"
                      />
                    </svg>
                    Explore Collections
                  </span>
                </button>
              </Link>
            </div>
          </motion.div>
        </div>
      </section>

      <Footer />
    </div>
  );
};

export default Home;