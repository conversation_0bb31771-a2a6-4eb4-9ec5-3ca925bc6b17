/**
 * Centralized product collections data
 * This file manages all product data and integrates with dynamic image loading
 */

import { loadProductCollections } from '../utils/imageLoader';

// Cache for loaded collections
let cachedCollections = null;
let loadingPromise = null;

/**
 * Get product collections with processed images
 * @returns {Promise<Object>} - Object containing watches and bracelets collections
 */
export const getProductCollections = async () => {
  // Return cached data if available
  if (cachedCollections) {
    return cachedCollections;
  }

  // Return existing promise if already loading
  if (loadingPromise) {
    return loadingPromise;
  }

  // Start loading
  loadingPromise = loadCollectionsWithDetails();
  cachedCollections = await loadingPromise;
  loadingPromise = null;

  return cachedCollections;
};

/**
 * Load collections with detailed product information
 * @returns {Promise<Object>} - Detailed product collections
 */
const loadCollectionsWithDetails = async () => {
  try {
    // Load basic collections with processed images
    const basicCollections = await loadProductCollections();

    // Enhance with detailed information
    const enhancedCollections = {
      watches: enhanceWatchProducts(basicCollections.watches),
      bracelets: enhanceBraceletProducts(basicCollections.bracelets),
      rings: enhanceRingProducts(basicCollections.rings),
      earrings: enhanceEarringProducts(basicCollections.earrings)
    };

    return enhancedCollections;
  } catch (error) {
    console.error('Error loading product collections:', error);
    return getFallbackCollections();
  }
};

/**
 * Enhance watch products with detailed information
 * @param {Array} watches - Basic watch products
 * @returns {Array} - Enhanced watch products
 */
const enhanceWatchProducts = (watches) => {
  const watchDetails = [
    {
      description: 'This elegant dress watch features a timeless design with a genuine leather strap and stainless steel case. Perfect for formal occasions and business attire.',
      features: [
        'Genuine leather strap',
        'Stainless steel case',
        'Water resistant up to 30m',
        'Scratch-resistant sapphire crystal',
        'Japanese quartz movement'
      ],
      specifications: {
        'Case Diameter': '40mm',
        'Case Thickness': '10mm',
        'Band Width': '20mm',
        'Band Material': 'Genuine Leather',
        'Movement': 'Japanese Quartz',
        'Water Resistance': '30m'
      },
      categories: ['mens', 'luxury']
    },
    {
      description: 'A versatile sport chronograph with multiple subdials and a durable silicone strap. Designed for active lifestyles with enhanced water resistance.',
      features: [
        'Chronograph functionality',
        'Silicone strap',
        'Stainless steel case',
        'Water resistant up to 100m',
        'Luminous hands and markers',
        'Tachymeter scale'
      ],
      specifications: {
        'Case Diameter': '42mm',
        'Case Thickness': '12mm',
        'Band Width': '22mm',
        'Band Material': 'Silicone',
        'Movement': 'Swiss Quartz',
        'Water Resistance': '100m'
      },
      categories: ['mens', 'new']
    },
    {
      description: 'A sleek minimalist watch with a clean dial and mesh bracelet. The perfect accessory for those who appreciate understated elegance.',
      features: [
        'Mesh stainless steel bracelet',
        'Ultra-thin case design',
        'Sapphire crystal glass',
        'Japanese movement',
        'Date function'
      ],
      specifications: {
        'Case Diameter': '38mm',
        'Case Thickness': '7mm',
        'Band Width': '18mm',
        'Band Material': 'Stainless Steel Mesh',
        'Movement': 'Japanese Quartz',
        'Water Resistance': '50m'
      },
      categories: ['womens']
    },
    {
      description: 'An elegant gold-plated watch with premium finishing and sophisticated design elements.',
      features: [
        '18k gold plating',
        'Premium leather strap',
        'Swiss movement',
        'Scratch-resistant crystal',
        'Water resistant'
      ],
      specifications: {
        'Case Diameter': '36mm',
        'Case Thickness': '9mm',
        'Band Width': '18mm',
        'Band Material': 'Premium Leather',
        'Movement': 'Swiss Quartz',
        'Water Resistance': '50m'
      },
      categories: ['womens', 'luxury', 'new']
    },
    {
      description: 'Professional diving watch with rotating bezel and enhanced water resistance for underwater adventures.',
      features: [
        'Rotating diving bezel',
        'Stainless steel construction',
        'Luminous markers',
        'Screw-down crown',
        'Professional water resistance'
      ],
      specifications: {
        'Case Diameter': '44mm',
        'Case Thickness': '14mm',
        'Band Width': '22mm',
        'Band Material': 'Stainless Steel',
        'Movement': 'Automatic',
        'Water Resistance': '200m'
      },
      categories: ['mens']
    },
    {
      description: 'Modern smartwatch with fitness tracking and connectivity features in a stylish package.',
      features: [
        'Fitness tracking',
        'Heart rate monitor',
        'Bluetooth connectivity',
        'Touch screen display',
        'Long battery life'
      ],
      specifications: {
        'Case Diameter': '42mm',
        'Case Thickness': '11mm',
        'Band Width': '20mm',
        'Band Material': 'Silicone',
        'Battery Life': '7 days',
        'Water Resistance': '50m'
      },
      categories: ['mens', 'womens', 'new']
    }
  ];

  return watches.map((watch, index) => ({
    ...watch,
    images: [watch.image, watch.originalImage], // Include both processed and original
    ...watchDetails[index] || watchDetails[0]
  }));
};

/**
 * Enhance bracelet products with detailed information
 * @param {Array} bracelets - Basic bracelet products
 * @returns {Array} - Enhanced bracelet products
 */
const enhanceBraceletProducts = (bracelets) => {
  const braceletDetails = [
    {
      description: 'A timeless chain bracelet crafted from sterling silver with a secure lobster clasp. Versatile enough for everyday wear or special occasions.',
      features: [
        'Sterling silver',
        'Lobster clasp closure',
        'Adjustable length',
        'Tarnish resistant',
        'Handcrafted details'
      ],
      specifications: {
        'Material': 'Sterling Silver',
        'Length': '7.5 inches',
        'Width': '4mm',
        'Clasp Type': 'Lobster',
        'Weight': '12g'
      },
      categories: ['womens', 'new']
    },
    {
      description: 'A contemporary bangle with a unique geometric design. Made from high-quality gold-plated brass for a luxurious look and feel.',
      features: [
        '18k gold plating',
        'Brass core',
        'Hinged design for easy wear',
        'Hypoallergenic',
        'Signature gift box included'
      ],
      specifications: {
        'Material': 'Gold-plated Brass',
        'Diameter': '2.5 inches',
        'Width': '8mm',
        'Plating': '18k Gold',
        'Weight': '18g'
      },
      categories: ['womens']
    },
    {
      description: 'A delicate charm bracelet featuring customizable pendants. Each charm is carefully crafted and can be arranged to tell your unique story.',
      features: [
        'Customizable charms',
        'Sterling silver chain',
        'Adjustable length',
        'Secure toggle clasp',
        'Additional charms available separately'
      ],
      specifications: {
        'Material': 'Sterling Silver',
        'Length': '6.5-8 inches',
        'Chain Type': 'Cable',
        'Number of Charms': '3',
        'Clasp Type': 'Toggle',
        'Weight': '10g'
      },
      categories: ['womens', 'charm']
    },
    {
      description: 'A sophisticated silver link bracelet with polished finish and secure clasp mechanism.',
      features: [
        'Polished silver links',
        'Secure clasp',
        'Adjustable sizing',
        'Durable construction',
        'Classic design'
      ],
      specifications: {
        'Material': 'Sterling Silver',
        'Length': '8 inches',
        'Width': '6mm',
        'Clasp Type': 'Box',
        'Weight': '15g'
      },
      categories: ['mens']
    },
    {
      description: 'Elegant bracelet with gold accent details and premium finishing.',
      features: [
        'Gold accent details',
        'Premium materials',
        'Comfortable fit',
        'Elegant design',
        'Gift packaging'
      ],
      specifications: {
        'Material': 'Gold-plated Steel',
        'Length': '7 inches',
        'Width': '5mm',
        'Plating': '14k Gold',
        'Weight': '14g'
      },
      categories: ['womens', 'new']
    },
    {
      description: 'Stylish leather wrap bracelet with adjustable design for comfortable all-day wear.',
      features: [
        'Genuine leather',
        'Wrap-around design',
        'Adjustable fit',
        'Metal accents',
        'Casual style'
      ],
      specifications: {
        'Material': 'Genuine Leather',
        'Length': 'Adjustable',
        'Width': '10mm',
        'Closure': 'Snap',
        'Weight': '8g'
      },
      categories: ['mens', 'new']
    }
  ];

  return bracelets.map((bracelet, index) => ({
    ...bracelet,
    images: [bracelet.image, bracelet.originalImage], // Include both processed and original
    ...braceletDetails[index] || braceletDetails[0]
  }));
};

/**
 * Enhance ring products with detailed information
 * @param {Array} rings - Basic ring products
 * @returns {Array} - Enhanced ring products
 */
const enhanceRingProducts = (rings) => {
  const ringDetails = [
    {
      description: 'A stunning diamond solitaire ring featuring a brilliant-cut diamond set in 18k white gold. The perfect symbol of everlasting love and commitment.',
      features: [
        'Brilliant-cut diamond',
        '18k white gold setting',
        'Four-prong setting',
        'Diamond certificate included',
        'Lifetime warranty'
      ],
      specifications: {
        'Metal': '18k White Gold',
        'Stone': 'Diamond',
        'Carat Weight': '1.0ct',
        'Color': 'G',
        'Clarity': 'VS1',
        'Cut': 'Excellent'
      },
      categories: ['womens', 'luxury', 'engagement']
    },
    {
      description: 'A classic gold band ring with a timeless design. Perfect for everyday wear or as a wedding band.',
      features: [
        '14k yellow gold',
        'Comfort fit design',
        'Polished finish',
        'Hypoallergenic',
        'Traditional style'
      ],
      specifications: {
        'Metal': '14k Yellow Gold',
        'Width': '4mm',
        'Finish': 'Polished',
        'Fit': 'Comfort',
        'Weight': '3.2g'
      },
      categories: ['mens', 'womens', 'wedding']
    },
    {
      description: 'An elegant silver ring with a modern design. Perfect for those who appreciate minimalist jewelry.',
      features: [
        'Sterling silver',
        'Contemporary design',
        'Adjustable sizing',
        'Tarnish resistant',
        'Lightweight'
      ],
      specifications: {
        'Metal': 'Sterling Silver',
        'Width': '3mm',
        'Finish': 'Polished',
        'Weight': '2.1g',
        'Sizing': 'Adjustable'
      },
      categories: ['womens', 'new']
    },
    {
      description: 'A beautiful gemstone ring featuring a vibrant sapphire surrounded by diamonds. A statement piece for special occasions.',
      features: [
        'Natural sapphire center stone',
        'Diamond accents',
        '18k white gold setting',
        'Birthstone option',
        'Gift box included'
      ],
      specifications: {
        'Metal': '18k White Gold',
        'Center Stone': 'Sapphire',
        'Carat Weight': '2.5ct',
        'Accent Stones': 'Diamonds',
        'Total Carat Weight': '3.2ct'
      },
      categories: ['womens', 'luxury', 'new']
    },
    {
      description: 'A traditional wedding band with a subtle pattern. Designed for comfort and durability for a lifetime of wear.',
      features: [
        '14k white gold',
        'Traditional pattern',
        'Comfort fit',
        'Durable construction',
        'Classic design'
      ],
      specifications: {
        'Metal': '14k White Gold',
        'Width': '6mm',
        'Pattern': 'Traditional',
        'Fit': 'Comfort',
        'Weight': '4.8g'
      },
      categories: ['mens', 'womens', 'wedding']
    }
  ];

  return rings.map((ring, index) => ({
    ...ring,
    images: [ring.image, ring.originalImage], // Include both processed and original
    ...ringDetails[index] || ringDetails[0]
  }));
};

/**
 * Enhance earring products with detailed information
 * @param {Array} earrings - Basic earring products
 * @returns {Array} - Enhanced earring products
 */
const enhanceEarringProducts = (earrings) => {
  const earringDetails = [
    {
      description: 'Classic diamond stud earrings featuring brilliant-cut diamonds in a secure four-prong setting. Perfect for everyday elegance.',
      features: [
        'Brilliant-cut diamonds',
        '18k white gold setting',
        'Four-prong setting',
        'Screw-back closure',
        'Diamond certificate included'
      ],
      specifications: {
        'Metal': '18k White Gold',
        'Stone': 'Diamond',
        'Carat Weight': '0.5ct each',
        'Color': 'G',
        'Clarity': 'VS2',
        'Closure': 'Screw-back'
      },
      categories: ['womens', 'luxury', 'new']
    },
    {
      description: 'Elegant gold hoop earrings with a timeless design. Perfect for both casual and formal occasions.',
      features: [
        '14k yellow gold',
        'Classic hoop design',
        'Post and butterfly closure',
        'Hypoallergenic',
        'Lightweight construction'
      ],
      specifications: {
        'Metal': '14k Yellow Gold',
        'Diameter': '20mm',
        'Width': '2mm',
        'Closure': 'Post and Butterfly',
        'Weight': '3.5g'
      },
      categories: ['womens', 'new']
    },
    {
      description: 'Delicate pearl drop earrings featuring freshwater pearls with sterling silver findings. A sophisticated choice for any occasion.',
      features: [
        'Freshwater pearls',
        'Sterling silver findings',
        'Drop design',
        'Fish hook closure',
        'Gift box included'
      ],
      specifications: {
        'Pearl Type': 'Freshwater',
        'Pearl Size': '8mm',
        'Metal': 'Sterling Silver',
        'Length': '25mm',
        'Closure': 'Fish Hook'
      },
      categories: ['womens', 'pearl']
    },
    {
      description: 'Modern silver earrings with a contemporary design. Perfect for those who appreciate minimalist jewelry with a touch of elegance.',
      features: [
        'Sterling silver',
        'Contemporary design',
        'Post and butterfly closure',
        'Tarnish resistant',
        'Lightweight'
      ],
      specifications: {
        'Metal': 'Sterling Silver',
        'Design': 'Geometric',
        'Size': '15mm',
        'Closure': 'Post and Butterfly',
        'Weight': '2.8g'
      },
      categories: ['womens', 'new']
    },
    {
      description: 'Vibrant gemstone earrings featuring natural stones in a beautiful setting. Each pair is unique with its own color variations.',
      features: [
        'Natural gemstones',
        'Sterling silver setting',
        'Drop design',
        'Fish hook closure',
        'Birthstone options available'
      ],
      specifications: {
        'Stone Type': 'Natural Gemstone',
        'Setting': 'Sterling Silver',
        'Length': '30mm',
        'Closure': 'Fish Hook',
        'Weight': '4.2g'
      },
      categories: ['womens', 'gemstone']
    }
  ];

  return earrings.map((earring, index) => ({
    ...earring,
    images: [earring.image, earring.originalImage], // Include both processed and original
    ...earringDetails[index] || earringDetails[0]
  }));
};

/**
 * Get fallback collections if dynamic loading fails
 * @returns {Object} - Fallback product collections
 */
const getFallbackCollections = () => {
  return {
    watches: [
      { id: "watch_1", name: 'Classic Dress Watch', image: '/imgs/watches/watch_1.png', price: '$599' },
      { id: "watch_2", name: 'Sport Chronograph', image: '/imgs/watches/watch_2.png', price: '$799' },
      { id: "watch_3", name: 'Minimalist Design', image: '/imgs/watches/watch_3.png', price: '$499' },
      { id: "watch_4", name: 'Rose Gold Luxury', image: '/imgs/watches/watch_4.png', price: '$899' },
      { id: "watch_5", name: 'Minimalist White', image: '/imgs/watches/watch_5.png', price: '$399' },
      { id: "watch_6", name: 'Sport Blue', image: '/imgs/watches/watch_6.png', price: '$699' },
    ],
    bracelets: [
      { id: "bracelet_1", name: 'Silver Chain', image: '/imgs/bracelets/bracelet_1.png', price: '$299' },
      { id: "bracelet_2", name: 'Gold Bangle', image: '/imgs/bracelets/bracelet_2.png', price: '$349' },
      { id: "bracelet_3", name: 'Leather Wrap', image: '/imgs/bracelets/bracelet_3.png', price: '$249' },
      { id: "bracelet_4", name: 'Diamond Tennis', image: '/imgs/bracelets/bracelet_4.png', price: '$599' },
      { id: "bracelet_5", name: 'Beaded Stone', image: '/imgs/bracelets/bracelet_5.png', price: '$199' },
      { id: "bracelet_6", name: 'Charm Bracelet', image: '/imgs/bracelets/bracelet_6.png', price: '$399' },
    ],
    rings: [
      { id: "ring_1", name: 'Diamond Solitaire', image: '/imgs/rings/ring_1.jpg', price: '$899' },
      { id: "ring_2", name: 'Gold Band', image: '/imgs/rings/ring_2.jpg', price: '$599' },
      { id: "ring_3", name: 'Silver Ring', image: '/imgs/rings/ring_3.jpg', price: '$399' },
      { id: "ring_4", name: 'Gemstone Ring', image: '/imgs/rings/ring_4.jpg', price: '$699' },
      { id: "ring_5", name: 'Wedding Band', image: '/imgs/rings/ring_5.jpg', price: '$499' },
    ],
    earrings: [
      { id: "earring_1", name: 'Diamond Studs', image: '/imgs/earrings/earring_1.png', price: '$599' },
      { id: "earring_2", name: 'Gold Hoops', image: '/imgs/earrings/earring_2.png', price: '$399' },
      { id: "earring_3", name: 'Pearl Drops', image: '/imgs/earrings/earring_3.png', price: '$299' },
      { id: "earring_4", name: 'Silver Earrings', image: '/imgs/earrings/earring_4.png', price: '$249' },
      { id: "earring_5", name: 'Gemstone Earrings', image: '/imgs/earrings/earring_5.png', price: '$349' },
    ]
  };
};

/**
 * Clear cached collections (useful for refreshing data)
 */
export const clearCache = () => {
  cachedCollections = null;
  loadingPromise = null;
};

/**
 * Get collections synchronously (returns cached data or fallback)
 * @returns {Object} - Product collections
 */
export const getProductCollectionsSync = () => {
  return cachedCollections || getFallbackCollections();
};
