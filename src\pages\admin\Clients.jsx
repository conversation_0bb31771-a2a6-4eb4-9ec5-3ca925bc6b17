import React, { useState, useEffect } from 'react';
import AdminSidebar from '../../components/admin/AdminSidebar';
import AdminNavbar from '../../components/admin/AdminNavbar';
import { motion, AnimatePresence } from 'framer-motion';
import {
  Search, Plus, Eye, Edit, Trash2, Globe, TrendingUp, Users, Code,
  X, Copy, Check, Clock, Smartphone, Activity,
  Calendar, Target, Zap, MapPin, ChevronRight
} from 'lucide-react';

function generatePassword() {
  const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*()_+';
  let password = '';
  for (let i = 0; i < 12; i++) {
    password += chars.charAt(Math.floor(Math.random() * chars.length));
  }
  return password;
}

const Clients = () => {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);
  const [collapsed, setCollapsed] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedStatus, setSelectedStatus] = useState('all');
  const [showModal, setShowModal] = useState(false);
  const [editingClient, setEditingClient] = useState(null);
  const [clients, setClients] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    newClientsThisMonth: 0,
    activeRate: 0,
    tryOnsGrowth: 0,
    uniqueUsers: 0
  });
  const [uniqueUsersData, setUniqueUsersData] = useState(null);
  const [showDetailsPopup, setShowDetailsPopup] = useState(false);
  const [showCodePopup, setShowCodePopup] = useState(false);
  const [selectedClientForDetails, setSelectedClientForDetails] = useState(null);
  const [selectedClientForCode, setSelectedClientForCode] = useState(null);
  const [clientAnalytics, setClientAnalytics] = useState(null);
  const [loadingAnalytics, setLoadingAnalytics] = useState(false);
  const [copiedCode, setCopiedCode] = useState(false);
  const [codeOptions, setCodeOptions] = useState({
    productImageUrl: 'YOUR_PRODUCT_IMAGE_URL',
    productName: '',
    productSize: '42',
    productType: 'watches',
    buttonStyle: 'primary',
    buttonSize: 'medium',
    buttonText: 'Try On Virtually'
  });
  const [clientForm, setClientForm] = useState({
    companyName: '',
    contactName: '',
    website: '',
    email: '',
    password: '',
    phone: '',
    industry: '',
    productType: 'watches',
    subscriptionPlan: 'basic'
  });
  const [showDeletePopup, setShowDeletePopup] = useState(false);
  const [clientToDelete, setClientToDelete] = useState(null);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  // Calculate margin for main content
  const mainMargin = collapsed ? 'md:ml-[80px]' : 'md:ml-[280px]';

  // Fetch clients from backend
  useEffect(() => {
    fetchClients();
  }, [searchQuery, selectedStatus]);

  const fetchClients = async () => {
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const params = new URLSearchParams();
      if (searchQuery) params.append('search', searchQuery);
      if (selectedStatus !== 'all') {
        if (selectedStatus === 'inactive') {
          params.append('isActive', 'false');
        } else {
          params.append('status', selectedStatus);
        }
      }

      // Fetch clients and unique users data in parallel
      const [clientsResponse, uniqueUsersResponse] = await Promise.all([
        fetch(`${apiUrl}/api/clients?${params}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/admin/unique-users`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      if (!clientsResponse.ok) {
        const errorData = await clientsResponse.json();
        throw new Error(errorData.message || 'Failed to fetch clients');
      }

      const clientsData = await clientsResponse.json();
      // Only filter out inactive clients when not specifically requesting them
      setClients(selectedStatus === 'inactive' 
        ? (clientsData.clients || []).filter(client => client.isActive === false)
        : (clientsData.clients || []).filter(client => client.isActive !== false)
      );

      // Handle unique users data
      let uniqueUsersCount = 0;
      if (uniqueUsersResponse.ok) {
        const uniqueUsersData = await uniqueUsersResponse.json();
        setUniqueUsersData(uniqueUsersData);
        uniqueUsersCount = uniqueUsersData.summary?.totalUniqueUsers || 0;
      }

      setStats({
        newClientsThisMonth: clientsData.stats?.newClientsThisMonth || 0,
        activeRate: clientsData.stats?.activeRate || 0,
        tryOnsGrowth: clientsData.stats?.tryOnsGrowth || 0,
        uniqueUsers: uniqueUsersCount
      });

    } catch (err) {
      console.error('Error fetching clients:', err);
      setError(err.message);
      setClients([]);
      setStats({
        newClientsThisMonth: 0,
        activeRate: 0,
        tryOnsGrowth: 0,
        uniqueUsers: 0
      });
    } finally {
      setLoading(false);
    }
  };

  // Helper function to format last active time
  const formatLastActive = (date) => {
    if (!date) return 'Never';
    const now = new Date();
    const lastActive = new Date(date);
    const diffInHours = Math.floor((now - lastActive) / (1000 * 60 * 60));

    if (diffInHours < 1) return 'Just now';
    if (diffInHours < 24) return `${diffInHours} hours ago`;
    const diffInDays = Math.floor(diffInHours / 24);
    if (diffInDays < 7) return `${diffInDays} days ago`;
    const diffInWeeks = Math.floor(diffInDays / 7);
    return `${diffInWeeks} weeks ago`;
  };

  const handleFormChange = (e) => {
    const { name, value } = e.target;
    setClientForm(prev => ({ ...prev, [name]: value }));
  };

  const handleSuggestPassword = () => {
    setClientForm(prev => ({ ...prev, password: generatePassword() }));
  };

  const resetForm = () => {
    setClientForm({
      companyName: '',
      contactName: '',
      website: '',
      email: '',
      password: '',
      phone: '',
      industry: '',
      productType: 'watches',
      subscriptionPlan: 'basic'
    });
    setEditingClient(null);
  };

  const handleAddClient = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientForm)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to create client');
      }

      await fetchClients();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error creating client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleEditClient = async (e) => {
    e.preventDefault();
    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients/${editingClient._id}`, {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        },
        body: JSON.stringify(clientForm)
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to update client');
      }

      await fetchClients();
      setShowModal(false);
      resetForm();
    } catch (err) {
      console.error('Error updating client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteClick = (client) => {
    setClientToDelete(client);
    setShowDeletePopup(true);
  };

  const handleDeleteConfirm = async () => {
    if (!clientToDelete) return;

    try {
      setLoading(true);
      setError(null);

      const token = localStorage.getItem('token');
      if (!token) {
        throw new Error('No authentication token found');
      }

      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      const response = await fetch(`${apiUrl}/api/clients/${clientToDelete._id}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to delete client');
      }

      // Remove the client from the local state immediately
      setClients(prevClients => prevClients.filter(client => client._id !== clientToDelete._id));
      
      // Close the popup and reset state
      setShowDeletePopup(false);
      setClientToDelete(null);

      // Refresh the full client list
      await fetchClients();
    } catch (err) {
      console.error('Error deleting client:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const openEditModal = (client) => {
    setEditingClient(client);
    setClientForm({
      companyName: client.companyName || '',
      contactName: client.contactName || '',
      website: client.website || '',
      email: client.email || '',
      password: '', // Don't pre-fill password
      phone: client.phone || '',
      industry: client.industry || '',
      productType: client.productType || 'watches',
      subscriptionPlan: client.subscriptionPlan || 'basic'
    });
    setShowModal(true);
  };

  const openAddModal = () => {
    resetForm();
    setShowModal(true);
  };

  // Fetch client analytics for details popup
  const fetchClientAnalytics = async (clientId) => {
    try {
      setLoadingAnalytics(true);
      const token = localStorage.getItem('token');
      const baseUrl = process.env.REACT_APP_API_URL || 'https://web-production-d8095.up.railway.app';
      const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

      // Calculate date range for last 30 days
      const end = new Date();
      const start = new Date();
      start.setDate(start.getDate() - 30);

      console.log('Fetching analytics for client:', clientId);
      console.log('Date range:', { start: start.toISOString(), end: end.toISOString() });

      // Fetch analytics from admin APIs
      const [clientDetailResponse, clientPerformanceResponse, productResponse, deviceResponse] = await Promise.all([
        fetch(`${apiUrl}/api/clients/${clientId}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/admin/client-performance?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/admin/product?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        }),
        fetch(`${apiUrl}/api/analytics/admin/devices?start=${start.toISOString()}&end=${end.toISOString()}`, {
          headers: {
            'Authorization': `Bearer ${token}`,
            'Content-Type': 'application/json'
          }
        })
      ]);

      console.log('API Responses:', {
        clientDetail: clientDetailResponse.status,
        clientPerformance: clientPerformanceResponse.status,
        product: productResponse.status,
        device: deviceResponse.status
      });

      let clientSpecificData = null;
      let clientPerformanceData = null;
      let productData = null;
      let deviceData = null;

      if (clientDetailResponse.ok) {
        clientSpecificData = await clientDetailResponse.json();
        console.log('Client Detail Data:', clientSpecificData);
      }
      if (clientPerformanceResponse.ok) {
        clientPerformanceData = await clientPerformanceResponse.json();
        console.log('Client Performance Data:', clientPerformanceData);
      }
      if (productResponse.ok) {
        productData = await productResponse.json();
        console.log('Product Data:', productData);
      }
      if (deviceResponse.ok) {
        deviceData = await deviceResponse.json();
        console.log('Device Data:', deviceData);
      }

      // Get client specific performance data
      const clientStats = clientPerformanceData?.clients?.find(c => c._id === clientId) || {
        sessions: 0,
        avgDuration: 0
      };

      // Get total sessions and avg duration from client performance data
      const totalSessions = clientStats.sessions || 0;
      const avgDuration = Math.round(clientStats.avgDuration || 0);

      // Get device stats for this client
      const clientDevices = deviceData?.devices || [];
      const uniqueUsers = deviceData?.metrics?.totalSessions || 0;

      // Get daily trends from client performance data
      const dailyTrends = clientPerformanceData?.trends?.map(trend => ({
        date: trend.date,
        sessions: trend.sessions || 0,
        avgDuration: Math.round(trend.avgDuration || 0),
        uniqueUsers: trend.uniqueUsers || 0
      })) || [];

      // Get product data for this client
      const clientProducts = productData?.products || [];

      console.log('Processed Analytics:', {
        totalSessions,
        avgDuration,
        uniqueUsers,
        dailyTrends,
        clientProducts
      });

      // Combine the data sources
      const combinedAnalytics = {
        clientDetail: clientSpecificData,
        totalSessions,
        avgDuration,
        uniqueUsers,
        dailyTrends,
        products: clientProducts,
        devices: clientDevices
      };

      console.log('Final Combined Analytics:', combinedAnalytics);
      setClientAnalytics(combinedAnalytics);
    } catch (error) {
      console.error('Error fetching client analytics:', error);
      setClientAnalytics(null);
    } finally {
      setLoadingAnalytics(false);
    }
  };

  // Handle view details popup
  const handleViewDetails = (client) => {
    setSelectedClientForDetails(client);
    setShowDetailsPopup(true);
    fetchClientAnalytics(client._id);
  };

  // Handle view code popup
  const handleViewCode = (client) => {
    setSelectedClientForCode(client);
    // Initialize code options with client data
    setCodeOptions(prev => ({
      ...prev,
      productName: client.productName || '',
      productType: client.productType || 'watches',
      productSize: client.caseDimensions || (client.productType === 'bracelets' ? '15' : '42'),
      productImageUrl: client.productImageUrl || 'YOUR_PRODUCT_IMAGE_URL'
    }));
    setShowCodePopup(true);
  };

  // Generate integration code
  const generateIntegrationCode = (client) => {
    const baseUrl = process.env.REACT_APP_WEBSITE_URL || 'https://viatryon.com';
    const { productImageUrl, productName, productSize, productType, buttonStyle, buttonSize, buttonText } = codeOptions;

    const buttonStyles = {
      primary: {
        backgroundColor: '#2D8C88',
        color: 'white',
        border: 'none',
        hoverColor: '#236b68'
      },
      outline: {
        backgroundColor: 'transparent',
        color: '#2D8C88',
        border: '2px solid #2D8C88',
        hoverColor: '#2D8C88'
      },
      minimal: {
        backgroundColor: 'transparent',
        color: '#2D8C88',
        border: 'none',
        hoverColor: 'rgba(45, 140, 136, 0.1)'
      },
      dark: {
        backgroundColor: '#333',
        color: 'white',
        border: 'none',
        hoverColor: '#555'
      }
    };

    const buttonSizes = {
      small: { padding: '8px 16px', fontSize: '14px' },
      medium: { padding: '12px 24px', fontSize: '16px' },
      large: { padding: '16px 32px', fontSize: '18px' }
    };

    const style = buttonStyles[buttonStyle];
    const size = buttonSizes[buttonSize];

    return `<!-- ViaTryon Virtual Try-On Button for ${productName || 'Product'} -->
<!-- Client ID: ${client._id} | Product Type: ${productType} | Size: ${productSize}mm -->
<button
  onclick="openViaTryon('${productImageUrl}', '${client._id}', '${productSize}', '${productType}', '${productName || 'Product'}')"
  class="viatryon-btn viatryon-btn-${buttonStyle} viatryon-btn-${buttonSize}"
  style="
    background-color: ${style.backgroundColor};
    color: ${style.color};
    border: ${style.border};
    padding: ${size.padding};
    font-size: ${size.fontSize};
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
    transition: all 0.3s ease;
  "
  onmouseover="this.style.opacity='0.8'"
  onmouseout="this.style.opacity='1'"
>
  ${buttonText}
</button>

<script>
function openViaTryon(imageUrl, clientId, caseDimensions, productType, productName) {
  // Construct the ViaTryon URL with parameters including product name
  const tryonUrl = '${baseUrl}/tryon?' +
    'image=' + encodeURIComponent(imageUrl) +
    '&client=' + encodeURIComponent(clientId) +
    '&size=' + encodeURIComponent(caseDimensions) +
    '&type=' + encodeURIComponent(productType || 'watches') +
    '&name=' + encodeURIComponent(productName || 'Product');

  // Open in new window/tab
  window.open(tryonUrl, '_blank', 'width=400,height=800,scrollbars=yes,resizable=yes');
}
</script>

<!-- Usage Instructions:
1. Replace 'YOUR_PRODUCT_IMAGE_URL' with the actual URL of your product image
2. Update caseDimensions with the actual case size (e.g., '42' for 42mm)
3. Make sure the product image has a white background for best results
-->`;
  };

  // Copy code to clipboard
  const copyCodeToClipboard = () => {
    const code = generateIntegrationCode(selectedClientForCode);
    navigator.clipboard.writeText(code).then(() => {
      setCopiedCode(true);
      setTimeout(() => setCopiedCode(false), 2000);
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <AdminSidebar isOpen={isSidebarOpen} onClose={() => setIsSidebarOpen(false)} collapsed={collapsed} setCollapsed={setCollapsed} />
      <AdminNavbar toggleSidebar={toggleSidebar} collapsed={collapsed} />

      {/* Main Content */}
      <main className={`${mainMargin} pt-16 transition-all duration-300`}>
        <div className="p-4 md:p-6">
          {/* Page Header */}
          <div className="mb-6 flex flex-col md:flex-row md:items-center md:justify-between gap-4">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Client Management</h1>
              <p className="text-gray-600">Manage your virtual try-on clients and track their performance.</p>
            </div>
            <button
              className="inline-flex items-center px-4 py-2 bg-[#2D8C88] text-white rounded-lg shadow-sm hover:bg-[#236b68] focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:ring-offset-2"
              onClick={openAddModal}
            >
              <Plus className="h-4 w-4 mr-2" />
              Add Client
            </button>
          </div>

          {/* Stats Overview */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6 mb-6">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
                  <Users className="h-6 w-6 text-blue-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">+{stats.newClientsThisMonth} new</span>
                <span className="text-sm text-gray-600 ml-2">this month</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Active Clients</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.filter(c => c.subscriptionStatus === 'active').length}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-green-500/10 flex items-center justify-center">
                  <TrendingUp className="h-6 w-6 text-green-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-green-600">{stats.activeRate.toFixed(1)}%</span>
                <span className="text-sm text-gray-600 ml-2">active rate</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Try-Ons</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : clients.reduce((sum, c) => sum + (c.analytics?.totalSessions || 0), 0).toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
                  <Eye className="h-6 w-6 text-[#2D8C88]" />
                </div>
              </div>
              <div className="mt-4">
                <span className={`text-sm font-medium ${stats.tryOnsGrowth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                  {stats.tryOnsGrowth >= 0 ? '+' : ''}{stats.tryOnsGrowth.toFixed(1)}%
                </span>
                <span className="text-sm text-gray-600 ml-2">this month</span>
              </div>
            </motion.div>

            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
              className="bg-white rounded-xl shadow-sm p-6"
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Unique Users (by IP)</p>
                  <p className="text-2xl font-semibold text-gray-900 mt-1">{loading ? '...' : stats.uniqueUsers.toLocaleString()}</p>
                </div>
                <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
                  <Activity className="h-6 w-6 text-purple-500" />
                </div>
              </div>
              <div className="mt-4">
                <span className="text-sm font-medium text-blue-600">
                  {uniqueUsersData?.summary?.avgSessionsPerUser?.toFixed(1) || '0'} avg sessions
                </span>
                <span className="text-sm text-gray-600 ml-2">per user</span>
              </div>
            </motion.div>
          </div>

          {/* Add/Edit Client Modal */}
          <AnimatePresence>
            {showModal && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4"
              >
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-2xl shadow-2xl w-full max-w-lg relative overflow-hidden"
                >
                  {/* Header */}
                  <div className="bg-gradient-to-r from-[#2D8C88] to-[#236b68] px-6 py-4">
                    <div className="flex items-center justify-between">
                      <h2 className="text-xl font-bold text-white">
                        {editingClient ? 'Edit Client' : 'Add New Client'}
                      </h2>
                      <button
                        className="text-white/80 hover:text-white transition-colors p-1"
                        onClick={() => setShowModal(false)}
                      >
                        <X className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  {/* Form */}
                  <div className="p-6">
                    <form onSubmit={editingClient ? handleEditClient : handleAddClient} className="space-y-5">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Company Name</label>
                          <input
                            type="text"
                            name="companyName"
                            value={clientForm.companyName}
                            onChange={handleFormChange}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter company name"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Contact Name</label>
                          <input
                            type="text"
                            name="contactName"
                            value={clientForm.contactName}
                            onChange={handleFormChange}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter contact name"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Email</label>
                          <input
                            type="email"
                            name="email"
                            value={clientForm.email}
                            onChange={handleFormChange}
                            required
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter email address"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Phone</label>
                          <input
                            type="tel"
                            name="phone"
                            value={clientForm.phone}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="Enter phone number"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Website</label>
                          <input
                            type="url"
                            name="website"
                            value={clientForm.website}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="https://example.com"
                          />
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Industry</label>
                          <input
                            type="text"
                            name="industry"
                            value={clientForm.industry}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                            placeholder="e.g., Fashion, Jewelry"
                          />
                        </div>
                      </div>

                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Product Type</label>
                          <select
                            name="productType"
                            value={clientForm.productType}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                          >
                            <option value="watches">Watches</option>
                            <option value="bracelets">Bracelets</option>
                            <option value="both">Both</option>
                          </select>
                        </div>
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Subscription Plan</label>
                          <select
                            name="subscriptionPlan"
                            value={clientForm.subscriptionPlan}
                            onChange={handleFormChange}
                            className="w-full px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                          >
                            <option value="basic">Basic</option>
                            <option value="premium">Premium</option>
                            <option value="enterprise">Enterprise</option>
                          </select>
                        </div>
                      </div>

                      {!editingClient && (
                        <div>
                          <label className="block text-sm font-semibold text-gray-700 mb-2">Password</label>
                          <div className="flex gap-3">
                            <input
                              type="text"
                              name="password"
                              value={clientForm.password}
                              onChange={handleFormChange}
                              required
                              className="flex-1 px-4 py-3 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent transition-all"
                              placeholder="Enter password"
                            />
                            <button
                              type="button"
                              onClick={handleSuggestPassword}
                              className="px-4 py-3 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors font-medium"
                            >
                              Generate
                            </button>
                          </div>
                        </div>
                      )}

                      {/* Action Buttons */}
                      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-100">
                        <button
                          type="button"
                          onClick={() => setShowModal(false)}
                          className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                        >
                          Cancel
                        </button>
                        <button
                          type="submit"
                          className="px-6 py-3 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors font-medium shadow-sm"
                        >
                          {editingClient ? 'Update Client' : 'Create Client'}
                        </button>
                      </div>
                    </form>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Filters */}
          <div className="bg-white rounded-xl shadow-sm p-4 mb-6">
            <div className="flex flex-col md:flex-row gap-4">
              <div className="flex-1">
                <input
                  type="text"
                  placeholder="Search clients..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                />
              </div>
              <div className="w-full md:w-48">
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Not Active</option>
                  <option value="pending">Pending</option>
                </select>
              </div>
            </div>
          </div>

          {/* Clients Table */}
          <div className="bg-white rounded-xl shadow-sm overflow-hidden">
            <div className="overflow-x-auto">
              <table className="min-w-full divide-y divide-gray-200">
                <thead className="bg-gray-50">
                  <tr>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Client</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Try-Ons</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden md:table-cell">Status</th>
                    <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider hidden lg:table-cell">Integration</th>
                    <th className="px-4 py-3 text-right text-xs font-medium text-gray-500 uppercase tracking-wider">Actions</th>
                  </tr>
                </thead>
                <tbody className="bg-white divide-y divide-gray-200">
                  {loading ? (
                    <tr>
                      <td colSpan="5" className="px-4 py-8 text-center">
                        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88] mx-auto"></div>
                      </td>
                    </tr>
                  ) : error ? (
                    <tr>
                      <td colSpan="5" className="px-4 py-8 text-center text-red-600">
                        Error loading clients: {error}
                      </td>
                    </tr>
                  ) : clients.length === 0 ? (
                    <tr>
                      <td colSpan="5" className="px-4 py-8 text-center text-gray-500">
                        No clients found
                      </td>
                    </tr>
                  ) : (
                    clients.map((client) => (
                      <motion.tr
                        key={client._id}
                        initial={{ opacity: 0 }}
                        animate={{ opacity: 1 }}
                        className="hover:bg-gray-50"
                      >
                        <td className="px-4 py-4 whitespace-nowrap">
                          <div className="flex items-center">
                            <div className="flex-shrink-0 h-10 w-10">
                              <div className="h-10 w-10 rounded-full bg-[#2D8C88] flex items-center justify-center text-white">
                                {client.companyName?.charAt(0) || 'C'}
                              </div>
                            </div>
                            <div className="ml-4">
                              <div className="text-sm font-medium text-gray-900">{client.companyName}</div>
                              <div className="text-sm text-gray-500">{client.email}</div>
                              <div className="text-sm text-gray-500 lg:hidden">
                                {client.analytics?.totalSessions?.toLocaleString() || '0'} try-ons
                              </div>
                            </div>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <div className="text-sm font-medium text-gray-900">{client.analytics?.totalSessions?.toLocaleString() || '0'}</div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden md:table-cell">
                          <div className="flex flex-col space-y-1">
                            <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                              client.subscriptionStatus === 'active' ? 'bg-green-100 text-green-800' :
                              client.subscriptionStatus === 'trial' ? 'bg-blue-100 text-blue-800' :
                              'bg-yellow-100 text-yellow-800'
                            }`}>
                              {client.subscriptionStatus}
                            </span>
                            <span className="text-xs text-gray-500">{formatLastActive(client.analytics?.lastActive)}</span>
                          </div>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap hidden lg:table-cell">
                          <span className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            client.subscriptionPlan === 'enterprise' ? 'bg-purple-100 text-purple-800' :
                            client.subscriptionPlan === 'premium' ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                          }`}>
                            {client.subscriptionPlan}
                          </span>
                        </td>
                        <td className="px-4 py-4 whitespace-nowrap text-right text-sm font-medium">
                          <div className="flex justify-end space-x-2">
                            <button
                              className="text-[#2D8C88] hover:text-[#2D8C88]/80 p-2 rounded-lg hover:bg-[#2D8C88]/10 transition-colors"
                              onClick={() => handleViewDetails(client)}
                              title="View Details"
                            >
                              <Eye className="h-4 w-4" />
                            </button>
                            <button
                              className="text-blue-600 hover:text-blue-800 p-2 rounded-lg hover:bg-blue-50 transition-colors"
                              onClick={() => handleViewCode(client)}
                              title="Integration Code"
                            >
                              <Code className="h-4 w-4" />
                            </button>
                            <button
                              className="text-gray-600 hover:text-gray-800 p-2 rounded-lg hover:bg-gray-50 transition-colors"
                              onClick={() => openEditModal(client)}
                              title="Edit Client"
                            >
                              <Edit className="h-4 w-4" />
                            </button>
                            <button
                              className={`text-red-600 hover:text-red-800 p-2 rounded-lg hover:bg-red-50 transition-colors ${loading ? 'opacity-50 pointer-events-none' : ''}`}
                              onClick={() => handleDeleteClick(client)}
                              title="Delete Client"
                              disabled={loading}
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </motion.tr>
                    ))
                  )}
                </tbody>
              </table>
            </div>
          </div>

          {/* View Details Popup */}
          <AnimatePresence>
            {showDetailsPopup && selectedClientForDetails && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-2"
              >
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-xl shadow-lg w-full max-w-md p-6 relative overflow-y-auto" style={{ maxHeight: '90vh' }} >
                  {/* Header */}
                  <div className="flex items-center justify-between mb-4">
                    <div className="flex items-center space-x-3">
                      <div className="w-10 h-10 rounded-lg bg-[#2D8C88] flex items-center justify-center text-white font-bold text-lg">
                        {selectedClientForDetails.companyName?.charAt(0)?.toUpperCase() || 'C'}
                      </div>
                      <div>
                        <h2 className="text-lg font-bold text-gray-900">
                          {selectedClientForDetails.companyName}
                        </h2>
                        <p className="text-xs text-gray-500">Client Info</p>
                      </div>
                    </div>
                    <button
                      className="text-gray-400 hover:text-gray-700 p-1"
                      onClick={() => setShowDetailsPopup(false)}
                    >
                      <X className="h-5 w-5" />
                    </button>
                  </div>

                  {/* Client Info */}
                  <div className="space-y-2 mb-4">
                    <div className="text-sm"><span className="font-semibold">Contact:</span> {selectedClientForDetails.contactName || 'N/A'}</div>
                    <div className="text-sm"><span className="font-semibold">Email:</span> {selectedClientForDetails.email || 'N/A'}</div>
                    <div className="text-sm"><span className="font-semibold">Phone:</span> {selectedClientForDetails.phone || 'N/A'}</div>
                    <div className="text-sm"><span className="font-semibold">Website:</span> {selectedClientForDetails.website || 'N/A'}</div>
                    <div className="text-sm"><span className="font-semibold">Industry:</span> {selectedClientForDetails.industry || 'N/A'}</div>
                    <div className="text-sm"><span className="font-semibold">Product Type:</span> {selectedClientForDetails.productType || 'N/A'}</div>
                    <div className="text-sm"><span className="font-semibold">Subscription:</span> {selectedClientForDetails.subscriptionPlan || 'N/A'}</div>
                  </div>

                  {/* Analytics */}
                  {loadingAnalytics ? (
                    <div className="flex justify-center py-4">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#2D8C88]"></div>
                    </div>
                  ) : clientAnalytics ? (
                    <>
                      {/* Analytics Overview */}
                      <div className="grid grid-cols-2 gap-2 text-center mb-4">
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="text-xs text-gray-500">Total Sessions</div>
                          <div className="font-bold text-gray-900">{clientAnalytics.totalSessions.toLocaleString()}</div>
                        </div>
                        <div className="bg-gray-50 rounded-lg p-3">
                          <div className="text-xs text-gray-500">Avg Duration</div>
                          <div className="font-bold text-gray-900">{clientAnalytics.avgDuration}s</div>
                        </div>
                      </div>

                      {/* Recent Activity */}
                      {clientAnalytics.dailyTrends && clientAnalytics.dailyTrends.length > 0 && (
                        <div className="mt-4">
                          <div className="text-sm font-semibold text-gray-700 mb-2">Recent Activity (Last 7 Days)</div>
                          <div className="space-y-2 max-h-40 overflow-y-auto">
                            {clientAnalytics.dailyTrends.slice(-7).map((day, idx) => (
                              <div key={idx} className="flex justify-between items-center text-sm bg-gray-50 rounded-lg p-2">
                                <span className="text-gray-600">{new Date(day.date).toLocaleDateString()}</span>
                                <div className="flex items-center space-x-4">
                                  <span className="text-gray-900">{day.sessions} sessions</span>
                                  <span className="text-gray-500">{day.avgDuration}s avg</span>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </>
                  ) : (
                    <div className="text-center py-4 text-gray-500">
                      No analytics data available
                    </div>
                  )}
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* View Code Popup - EmbedCodeGenerator Style */}
          <AnimatePresence>
            {showCodePopup && selectedClientForCode && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50"
              >
                <motion.div
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-xl shadow-xl w-full max-w-4xl max-h-[90vh] overflow-y-auto"
                >
                  <div className="p-6 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                      <div>
                        <h2 className="text-xl font-semibold text-gray-900">Virtual Try-On Integration</h2>
                        <p className="text-gray-600">Generate embed code for {selectedClientForCode?.companyName || 'client'}</p>
                        <p className="text-sm text-gray-500 mt-1">Client ID: {selectedClientForCode?._id}</p>
                        {codeOptions.productName && (
                          <p className="text-sm text-gray-500 mt-1">Product: {codeOptions.productName}</p>
                        )}
                      </div>
                      <button
                        onClick={() => setShowCodePopup(false)}
                        className="text-gray-400 hover:text-gray-600"
                      >
                        <X className="h-6 w-6" />
                      </button>
                    </div>
                  </div>

                  <div className="p-6">
                    <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                      {/* Configuration Panel */}
                      <div className="space-y-6">
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 mb-4">Configuration</h3>

                          <div className="space-y-4">
                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Product Image URL
                              </label>
                              <input
                                type="text"
                                value={codeOptions.productImageUrl}
                                onChange={(e) => setCodeOptions(prev => ({ ...prev, productImageUrl: e.target.value }))}
                                placeholder="https://yoursite.com/images/watch.png"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                URL of the product image with transparent background
                              </p>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Product Name
                              </label>
                              <input
                                type="text"
                                value={codeOptions.productName}
                                onChange={(e) => setCodeOptions(prev => ({ ...prev, productName: e.target.value }))}
                                placeholder="Product Name"
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                Name of the product for analytics tracking
                              </p>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Product Type
                              </label>
                              <select
                                value={codeOptions.productType}
                                onChange={(e) => setCodeOptions(prev => ({ ...prev, productType: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                              >
                                <option value="watches">Watches</option>
                                <option value="bracelets">Bracelets</option>
                              </select>
                              <p className="text-xs text-gray-500 mt-1">
                                Select the type of product for optimal try-on experience
                              </p>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                {codeOptions.productType === 'watches' ? 'Case Dimensions (mm)' : 'Width (mm)'}
                              </label>
                              <input
                                type="number"
                                value={codeOptions.productSize}
                                onChange={(e) => setCodeOptions(prev => ({ ...prev, productSize: e.target.value }))}
                                placeholder={codeOptions.productType === 'watches' ? '42' : '15'}
                                min={codeOptions.productType === 'watches' ? '20' : '10'}
                                max={codeOptions.productType === 'watches' ? '60' : '30'}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                              />
                              <p className="text-xs text-gray-500 mt-1">
                                {codeOptions.productType === 'watches'
                                  ? 'Watch case diameter for proper scaling (20-60mm)'
                                  : 'Bracelet width for proper scaling (10-30mm)'
                                }
                              </p>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Button Style
                              </label>
                              <select
                                value={codeOptions.buttonStyle}
                                onChange={(e) => setCodeOptions(prev => ({ ...prev, buttonStyle: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                              >
                                <option value="default">Default</option>
                                <option value="primary">Primary</option>
                                <option value="outline">Outline</option>
                                <option value="minimal">Minimal</option>
                              </select>
                            </div>

                            <div>
                              <label className="block text-sm font-medium text-gray-700 mb-2">
                                Button Size
                              </label>
                              <select
                                value={codeOptions.buttonSize}
                                onChange={(e) => setCodeOptions(prev => ({ ...prev, buttonSize: e.target.value }))}
                                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#2D8C88] focus:border-transparent"
                              >
                                <option value="small">Small</option>
                                <option value="medium">Medium</option>
                                <option value="large">Large</option>
                              </select>
                            </div>
                          </div>
                        </div>

                        {/* Preview */}
                        <div>
                          <h3 className="text-lg font-medium text-gray-900 mb-4">Preview</h3>
                          <div className="border border-gray-200 rounded-lg p-4 bg-gray-50">
                            <div className="flex items-center justify-center">
                              <button
                                className={`inline-flex items-center px-4 py-2 rounded-lg font-medium transition-colors ${
                                  codeOptions.buttonStyle === 'primary' ? 'bg-[#2D8C88] text-white hover:bg-[#236b68]' :
                                  codeOptions.buttonStyle === 'outline' ? 'border-2 border-[#2D8C88] text-[#2D8C88] hover:bg-[#2D8C88] hover:text-white' :
                                  codeOptions.buttonStyle === 'minimal' ? 'text-[#2D8C88] hover:bg-[#2D8C88]/10' :
                                  'bg-gray-800 text-white hover:bg-gray-700'
                                } ${
                                  codeOptions.buttonSize === 'small' ? 'text-sm px-3 py-1.5' :
                                  codeOptions.buttonSize === 'large' ? 'text-lg px-6 py-3' :
                                  'text-base px-4 py-2'
                                }`}
                              >
                                <Globe className="h-4 w-4 mr-2" />
                                {codeOptions.buttonText}
                              </button>
                            </div>
                          </div>
                        </div>
                      </div>

                      {/* Code Panel */}
                      <div>
                        <div className="flex items-center justify-between mb-4">
                          <h3 className="text-lg font-medium text-gray-900">Embed Code</h3>
                          <button
                            onClick={copyCodeToClipboard}
                            className="inline-flex items-center px-3 py-2 bg-[#2D8C88] text-white rounded-lg hover:bg-[#236b68] transition-colors"
                          >
                            {copiedCode ? <Check className="h-4 w-4 mr-2" /> : <Copy className="h-4 w-4 mr-2" />}
                            {copiedCode ? 'Copied!' : 'Copy Code'}
                          </button>
                        </div>

                        <div className="bg-gray-900 rounded-lg p-4 overflow-x-auto">
                          <pre className="text-green-400 text-sm">
                            <code>{generateIntegrationCode(selectedClientForCode)}</code>
                          </pre>
                        </div>

                        <div className="mt-6 space-y-4">
                          <div className="bg-blue-50 border border-blue-200 rounded-lg p-4">
                            <h4 className="font-medium text-blue-900 mb-2">Integration Steps:</h4>
                            <ol className="text-sm text-blue-800 space-y-1">
                              <li>1. Copy the embed code above</li>
                              <li>2. Replace 'YOUR_PRODUCT_IMAGE_URL' with your actual product image URL</li>
                              <li>3. Update the dimensions (size) for each product</li>
                              <li>4. Your client ID is automatically included for analytics tracking</li>
                              <li>5. Ensure the product type matches your product category</li>
                              <li>6. Paste the code into your product page HTML</li>
                              <li>7. Test the integration on your website</li>
                            </ol>
                            <div className="mt-3 p-3 bg-blue-100 rounded-lg">
                              <p className="text-xs text-blue-700">
                                <strong>Note:</strong> Your client ID ({selectedClientForCode?._id}) is automatically included in the code for analytics tracking.
                              </p>
                            </div>
                          </div>

                          <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4">
                            <h4 className="font-medium text-yellow-900 mb-2">Need Help?</h4>
                            <p className="text-sm text-yellow-800">
                              Check our <a href="#" className="underline">integration guide</a> or
                              <a href="#" className="underline ml-1">contact support</a> for assistance.
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>

          {/* Delete Confirmation Popup */}
          <AnimatePresence>
            {showDeletePopup && clientToDelete && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
                className="fixed inset-0 z-50 flex items-center justify-center bg-black/60 p-4"
              >
                <motion.div
                  initial={{ scale: 0.95, opacity: 0 }}
                  animate={{ scale: 1, opacity: 1 }}
                  exit={{ scale: 0.95, opacity: 0 }}
                  className="bg-white rounded-xl shadow-lg w-full max-w-sm p-6"
                >
                  <div className="text-center">
                    <div className="mx-auto flex items-center justify-center h-12 w-12 rounded-full bg-red-100 mb-4">
                      <Trash2 className="h-6 w-6 text-red-600" />
                    </div>
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Delete Client</h3>
                    <p className="text-sm text-gray-500 mb-6">
                      Are you sure you want to delete {clientToDelete.companyName}? This action cannot be undone.
                    </p>
                    <div className="flex justify-center space-x-3">
                      <button
                        onClick={() => {
                          setShowDeletePopup(false);
                          setClientToDelete(null);
                        }}
                        className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors font-medium"
                      >
                        Cancel
                      </button>
                      <button
                        onClick={handleDeleteConfirm}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors font-medium"
                      >
                        Delete
                      </button>
                    </div>
                  </div>
                </motion.div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </main>
    </div>
  );
};

export default Clients;