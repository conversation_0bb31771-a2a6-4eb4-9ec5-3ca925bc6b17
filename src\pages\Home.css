/* Color Variables */
:root {
  --primary-green: #2D8C88;
  --secondary-green: #3a7d5c;
  --accent-orange: #F28C38;
  --light-green: #e8f5e9;
  --dark-text: #1F2937;
  --light-text: #ffffff;
  --gray-bg: #F9FAFB;
}

/* Base Styles */
.home-page {
  font-family: 'Inter', 'Montserrat', sans-serif;
  color: var(--dark-text);
  overflow-x: hidden;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1.5rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Button Styles */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 9999px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  transform: translateZ(0);
  backface-visibility: hidden;
}

.btn-primary {
  background-color: var(--primary-green);
  color: white;
  box-shadow: 0 4px 6px -1px rgba(45, 140, 136, 0.1), 0 2px 4px -1px rgba(45, 140, 136, 0.06);
}

.btn-primary:hover {
  transform: translateY(-2px) scale(1.02);
  box-shadow: 0 10px 15px -3px rgba(45, 140, 136, 0.1), 0 4px 6px -2px rgba(45, 140, 136, 0.05);
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(45deg, var(--accent-orange), var(--primary-green));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.btn-primary:hover::before {
  opacity: 1;
}

.btn-secondary {
  background-color: white;
  color: var(--dark-text);
  border: 1px solid #E5E7EB;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.btn-secondary:hover {
  border-color: var(--accent-orange);
  color: var(--accent-orange);
  transform: translateY(-2px);
  box-shadow: 0 4px 6px -1px rgba(242, 140, 56, 0.1), 0 2px 4px -1px rgba(242, 140, 56, 0.06);
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  overflow: hidden;
  background: linear-gradient(to bottom, white, var(--gray-bg));
  padding: 2rem 0;
}

.hero-content {
  position: relative;
  z-index: 2;
  padding: 1rem;
}

.hero-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(8px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 9999px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--primary-green);
  margin-bottom: 1.5rem;
}

.hero-title {
  font-size: clamp(2.5rem, 5vw, 4.5rem);
  font-weight: 700;
  margin-bottom: 1.5rem;
  line-height: 1.1;
  background: linear-gradient(135deg, var(--primary-green), var(--accent-orange));
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  color: var(--dark-text);
  opacity: 0.8;
  margin-bottom: 2rem;
  max-width: 600px;
  line-height: 1.6;
}

/* Product Display */
.product-display {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  perspective: 1000px;
}

.product-image {
  position: relative;
  width: 100%;
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 8px 25px rgba(0, 0, 0, 0.15));
  transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
  transform-style: preserve-3d;
  will-change: transform;
}

.product-image:hover {
  transform: scale(1.02) translateZ(20px);
}

/* Animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0) translateZ(0);
  }
  50% {
    transform: translateY(-20px) translateZ(20px);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.5;
    transform: scale(0.98);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px) translateZ(0);
  }
  to {
    opacity: 1;
    transform: translateY(0) translateZ(0);
  }
}

/* Loading States */
.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 0.5rem;
}

@keyframes loading {
  0% {
    background-position: 200% 0;
  }
  100% {
    background-position: -200% 0;
  }
}

/* Responsive Design */
@media (max-width: 1024px) {
  .container {
    padding: 0 1rem;
  }
  
  .hero-title {
    font-size: clamp(2rem, 4vw, 3.5rem);
  }
  
  .hero-subtitle {
    font-size: clamp(1rem, 1.5vw, 1.25rem);
  }

  .product-display {
    margin-bottom: 2rem;
  }
}

@media (max-width: 768px) {
  .hero-section {
    min-height: 100vh;
    text-align: center;
    padding: 1rem 0;
  }
  
  .hero-content {
    padding: 1rem 0;
  }
  
  .btn {
    width: 100%;
    margin-bottom: 1rem;
    padding: 0.875rem 1.5rem;
  }
  
  .product-display {
    height: 50vh;
    margin-bottom: 2rem;
  }

  .hero-badge {
    font-size: 0.75rem;
    padding: 0.375rem 0.75rem;
  }

  /* Fix for hero image in mobile */
  .hero-product-display {
    height: 40vh !important;
    min-height: 300px;
  }

  .hero-product-display img {
    max-height: 100%;
    width: auto;
    object-fit: contain;
  }
}

@media (max-width: 480px) {
  .container {
    padding: 0 0.75rem;
  }
  
  .hero-title {
    font-size: clamp(1.75rem, 3vw, 2.5rem);
  }
  
  .hero-subtitle {
    font-size: 1rem;
  }
  
  .btn {
    padding: 0.75rem 1.25rem;
    font-size: 0.875rem;
  }

  .product-display {
    height: 40vh;
  }

  /* Additional fixes for hero image in small mobile */
  .hero-product-display {
    height: 35vh !important;
    min-height: 250px;
  }

  .hero-product-display img {
    max-height: 90%;
    width: auto;
    object-fit: contain;
  }
}

/* Touch Device Optimizations */
@media (hover: none) {
  .btn:hover {
    transform: none;
  }
  
  .product-image:hover {
    transform: none;
  }

  .btn:active {
    transform: scale(0.98);
  }
}

/* Safari-specific fixes */
@supports (-webkit-touch-callout: none) {
  .hero-section {
    min-height: -webkit-fill-available;
  }
  
  .product-image {
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  .hero-badge {
    -webkit-backdrop-filter: blur(8px);
    backdrop-filter: blur(8px);
  }

  /* Fix for hero image in Safari mobile */
  .hero-product-display {
    height: 40vh !important;
    min-height: 300px;
  }

  .hero-product-display img {
    max-height: 100%;
    width: auto;
    object-fit: contain;
  }
}

/* Chrome-specific fixes */
@media screen and (-webkit-min-device-pixel-ratio:0) {
  .product-image {
    -webkit-backface-visibility: hidden;
    backface-visibility: hidden;
  }
}

/* Brand Showcase */
.brand-showcase {
  padding: 60px 0;
  background-color: var(--gray-bg);
}

.brand-showcase p {
  text-align: center;
  margin-bottom: 30px;
  font-size: 0.9rem;
  letter-spacing: 2px;
  color: var(--dark-text);
  opacity: 0.7;
}

.brand-logos {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
}

.brand-logos img {
  height: 40px;
  opacity: 0.7;
  transition: opacity 0.3s ease;
  margin: 15px 25px;
}

.brand-logos img:hover {
  opacity: 1;
}

/* Features Section */
.features-section {
  padding: 100px 0;
  background-color: white;
}

.features-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.feature-card {
  background-color: white;
  border-radius: 15px;
  padding: 40px 30px;
  text-align: center;
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 25px;
  background-color: var(--light-green);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.feature-icon img {
  width: 40px;
  height: 40px;
}

.feature-card h3 {
  font-size: 1.3rem;
  margin-bottom: 15px;
  color: var(--primary-green);
}

.feature-card p {
  color: var(--dark-text);
  opacity: 0.7;
  line-height: 1.6;
}

/* How It Works Section */
.how-it-works-section {
  padding: 100px 0;
  background-color: var(--gray-bg);
}

.steps-container {
  max-width: 800px;
  margin: 0 auto;
  position: relative;
}

.steps-container::before {
  content: '';
  position: absolute;
  left: 40px;
  top: 0;
  bottom: 0;
  width: 2px;
  background-color: var(--accent-green);
  opacity: 0.2;
}

.step {
  display: flex;
  margin-bottom: 50px;
  position: relative;
}

.step:last-child {
  margin-bottom: 0;
}

.step-number {
  width: 80px;
  height: 80px;
  background-color: var(--accent-green);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  font-weight: 700;
  flex-shrink: 0;
  margin-right: 30px;
  box-shadow: 0 5px 15px rgba(77, 170, 125, 0.3);
}

.step-content {
  padding-top: 15px;
}

.step-content h3 {
  font-size: 1.5rem;
  margin-bottom: 15px;
  color: var(--primary-green);
}

.step-content p {
  color: var(--dark-text);
  opacity: 0.8;
  line-height: 1.6;
}

/* Testimonials Section */
.testimonials-section {
  padding: 100px 0;
  background-color: white;
}

.testimonials-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-top: 50px;
}

.testimonial-card {
  background-color: white;
  border-radius: 15px;
  padding: 30px;
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.05);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.testimonial-card:hover {
  transform: translateY(-10px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.1);
}

.testimonial-content {
  margin-bottom: 25px;
  position: relative;
}

.testimonial-content p {
  font-style: italic;
  line-height: 1.6;
  position: relative;
  padding-left: 20px;
}

.testimonial-content p::before {
  content: '"';
  position: absolute;
  left: 0;
  top: -10px;
  font-size: 3rem;
  color: var(--accent-green);
  opacity: 0.2;
  font-family: serif;
}

.testimonial-author {
  display: flex;
  align-items: center;
}

.testimonial-author img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
  object-fit: cover;
  margin-right: 15px;
  border: 2px solid var(--light-green);
}

.testimonial-author h4 {
  font-size: 1.1rem;
  margin-bottom: 5px;
  color: var(--primary-green);
}

.testimonial-author p {
  font-size: 0.9rem;
  opacity: 0.7;
}

/* CTA Section */
.cta-section {
  position: relative;
  padding: 5rem 0;
  background: linear-gradient(135deg, var(--primary-green) 0%, #1F2937 50%, var(--primary-green) 100%);
  color: white;
  text-align: center;
  overflow: hidden;
}

.cta-background {
  position: absolute;
  inset: 0;
  opacity: 0.1;
}

.cta-background::before,
.cta-background::after {
  content: '';
  position: absolute;
  width: 20rem;
  height: 20rem;
  border-radius: 50%;
  filter: blur(80px);
  background: white;
}

.cta-background::before {
  top: 25%;
  left: -10rem;
}

.cta-background::after {
  bottom: 25%;
  right: -10rem;
}

.cta-content {
  position: relative;
  z-index: 1;
}

.cta-title {
  font-size: clamp(2rem, 4vw, 3rem);
  margin-bottom: 1.5rem;
  line-height: 1.2;
}

.cta-subtitle {
  font-size: clamp(1.125rem, 2vw, 1.5rem);
  margin-bottom: 3rem;
  opacity: 0.9;
  max-width: 42rem;
  margin-left: auto;
  margin-right: auto;
}

.cta-buttons {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  max-width: 24rem;
  margin: 0 auto;
}

@media (min-width: 640px) {
  .cta-buttons {
    flex-direction: row;
    max-width: none;
  }
}

.cta-button {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 1rem 2rem;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 9999px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
}

.cta-button-primary {
  background-color: white;
  color: var(--primary-green);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.cta-button-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.cta-button-secondary {
  background-color: transparent;
  color: white;
  border: 2px solid var(--accent-orange);
}

.cta-button-secondary:hover {
  background-color: var(--accent-orange);
  border-color: var(--accent-orange);
}