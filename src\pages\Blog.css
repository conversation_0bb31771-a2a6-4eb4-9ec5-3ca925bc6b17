/* Blog Page Styles */
.blog-hero {
  background: linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, rgba(242, 140, 56, 0.05) 100%);
}

.blog-card {
  transition: all 0.3s ease;
}

.blog-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.blog-category-badge {
  background: linear-gradient(135deg, #2D8C88 0%, #F28C38 100%);
  color: white;
  font-weight: 500;
  padding: 0.25rem 0.75rem;
  border-radius: 9999px;
  font-size: 0.75rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.blog-author-avatar {
  background: linear-gradient(135deg, #2D8C88 0%, #F28C38 100%);
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  font-size: 0.875rem;
}

.blog-featured-author-avatar {
  background: linear-gradient(135deg, #2D8C88 0%, #F28C38 100%);
  color: white;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  width: 2.5rem;
  height: 2.5rem;
  font-size: 1rem;
}

.blog-read-more {
  color: #2D8C88;
  font-weight: 500;
  transition: color 0.2s ease;
}

.blog-read-more:hover {
  color: #F28C38;
}

.blog-filter-button {
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.blog-filter-button:hover {
  transform: translateY(-1px);
}

.blog-filter-button.active {
  background: #2D8C88;
  color: white;
  border-color: #2D8C88;
}

.blog-load-more {
  background: white;
  color: #2D8C88;
  border: 2px solid #2D8C88;
  transition: all 0.3s ease;
}

.blog-load-more:hover {
  background: #2D8C88;
  color: white;
  transform: translateY(-2px);
  box-shadow: 0 10px 20px rgba(45, 140, 136, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .blog-hero h1 {
    font-size: 2.5rem;
  }
  
  .blog-card {
    margin-bottom: 1.5rem;
  }
  
  .blog-filter-button {
    font-size: 0.875rem;
    padding: 0.5rem 1rem;
  }
}

/* Animation classes */
.blog-fade-in {
  animation: fadeIn 0.6s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.blog-slide-up {
  animation: slideUp 0.8s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(40px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 