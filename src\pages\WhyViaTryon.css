/* Color Variables */
:root {
  --primary: #2D8C88;
  --secondary: #F28C38;
  --text-dark: #1F2937;
  --text-light: #6B7280;
  --background: #F9FAFB;
  --white: #FFFFFF;
}

/* Base Styles */
.why-viatryon-page {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  min-height: 100vh;
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
  line-height: 1.2;
  letter-spacing: -0.02em;
}

/* Button Styles */
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 9999px;
  transition: all 0.2s ease-in-out;
  cursor: pointer;
  overflow: hidden;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:hover {
  background-color: var(--secondary);
  transform: translateY(-1px);
  box-shadow: 0 6px 8px -1px rgba(0, 0, 0, 0.1), 0 4px 6px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary);
  border: 2px solid var(--primary);
}

.btn-secondary:hover {
  background-color: var(--primary);
  color: var(--white);
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  padding: 6rem 0;
  overflow: hidden;
  background: linear-gradient(135deg, rgba(45, 140, 136, 0.05) 0%, #F9FAFB 50%, rgba(242, 140, 56, 0.05) 100%);
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 3.75rem);
  line-height: 1.1;
  margin-bottom: 1.5rem;
}

.hero-subtitle {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  color: var(--text-light);
  margin-bottom: 2rem;
  max-width: 32rem;
}

.hero-image {
  position: relative;
  height: 40vh;
  transition: transform 0.3s ease-in-out;
}

.hero-image:hover {
  transform: scale(1.05);
}

.hero-image img {
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
}

/* Features Section */
.features-section {
  padding: 4rem 0;
  background-color: var(--white);
}

.feature-card {
  background-color: var(--background);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.feature-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.feature-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  background-color: rgba(45, 140, 136, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--primary);
  transition: all 0.2s ease-in-out;
}

.feature-card:hover .feature-icon {
  background-color: var(--primary);
  color: var(--white);
}

.feature-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
}

.feature-description {
  font-size: 0.875rem;
  color: var(--text-light);
  line-height: 1.5;
}

/* Use Cases Section */
.use-cases-section {
  padding: 4rem 0;
  background-color: var(--background);
}

.use-case-card {
  background-color: var(--white);
  border-radius: 1rem;
  padding: 1.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease-in-out;
}

.use-case-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.use-case-icon {
  width: 3rem;
  height: 3rem;
  border-radius: 9999px;
  background-color: rgba(45, 140, 136, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
  color: var(--primary);
  transition: all 0.2s ease-in-out;
}

.use-case-card:hover .use-case-icon {
  background-color: var(--primary);
  color: var(--white);
}

.use-case-title {
  font-size: 1.25rem;
  font-weight: 500;
  color: var(--text-dark);
  margin-bottom: 0.75rem;
}

.use-case-description {
  font-size: 0.875rem;
  color: var(--text-light);
  line-height: 1.5;
}

/* Responsive Design */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }

  .hero-image {
    height: 50vh;
  }
}

@media (min-width: 768px) {
  .hero-section {
    padding: 8rem 0;
  }

  .hero-image {
    height: 60vh;
  }

  .feature-card,
  .use-case-card {
    padding: 2rem;
  }

  .feature-title,
  .use-case-title {
    font-size: 1.5rem;
  }

  .feature-description,
  .use-case-description {
    font-size: 1rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2rem;
  }

  .hero-section {
    padding: 10rem 0;
  }

  .hero-image {
    height: 70vh;
  }
}

/* Touch Device Optimizations */
@media (hover: none) {
  .btn:hover,
  .feature-card:hover,
  .use-case-card:hover {
    transform: none;
  }

  .btn:active,
  .feature-card:active,
  .use-case-card:active {
    transform: scale(0.98);
  }
}

/* Safari Mobile Fixes */
@supports (-webkit-touch-callout: none) {
  .hero-image {
    height: 40vh;
  }

  @media (min-width: 640px) {
    .hero-image {
      height: 50vh;
    }
  }

  @media (min-width: 768px) {
    .hero-image {
      height: 60vh;
    }
  }

  @media (min-width: 1024px) {
    .hero-image {
      height: 70vh;
    }
  }
}

/* Chrome Mobile Fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .hero-image {
    transform: translateZ(0);
    backface-visibility: hidden;
  }
} 