// API service for interacting with the backend

const API_URL = process.env.REACT_APP_API_URL || 'http://localhost:5000/api';

/**
 * Save an image to the server
 * @param {string} imageData - Base64 encoded image data
 * @param {string} category - Product category (watches, bracelets)
 * @returns {Promise<Object>} - Response from the server
 */
export const saveImage = async (imageData, category) => {
  try {
    const response = await fetch(`${API_URL}/save-image`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ imageData, category }),
    });

    if (!response.ok) {
      throw new Error(`HTTP error! Status: ${response.status}`);
    }

    return await response.json();
  } catch (error) {
    console.error('Error saving image:', error);
    throw error;
  }
};

/**
 * Check if the server is running
 * @returns {Promise<boolean>} - True if server is running
 */
export const checkServerHealth = async () => {
  try {
    const response = await fetch(`${API_URL}/health`, {
      method: 'GET',
    });

    return response.ok;
  } catch (error) {
    console.error('Server health check failed:', error);
    return false;
  }
};


