/* Color Variables */
:root {
  --primary: #2D8C88;
  --secondary: #F28C38;
  --text: #1F2937;
  --background: #F9FAFB;
  --white: #FFFFFF;
}

/* Base Styles */
.contact-page {
  font-family: 'Inter', sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background-color: var(--background);
  color: var(--text);
}

.container {
  max-width: 1280px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* Typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Playfair Display', serif;
}

/* Buttons */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.75rem 1.5rem;
  border-radius: 9999px;
  font-weight: 500;
  transition: all 0.2s ease-in-out;
  min-height: 44px;
}

.btn-primary {
  background-color: var(--primary);
  color: var(--white);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-primary:hover {
  background-color: var(--secondary);
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

.btn-secondary {
  background-color: var(--white);
  color: var(--primary);
  border: 2px solid var(--primary);
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.btn-secondary:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-1px);
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
}

/* Hero Section */
.hero-section {
  position: relative;
  min-height: 100vh;
  padding: 6rem 0;
  overflow: hidden;
}

.hero-content {
  position: relative;
  z-index: 10;
}

.hero-badge {
  display: inline-block;
  padding: 0.5rem 1rem;
  background-color: rgba(255, 255, 255, 0.8);
  backdrop-filter: blur(8px);
  border-radius: 9999px;
  margin-bottom: 1.5rem;
}

.hero-title {
  font-size: clamp(2rem, 5vw, 4rem);
  line-height: 1.2;
  margin-bottom: 1.5rem;
}

.hero-description {
  font-size: clamp(1rem, 2vw, 1.25rem);
  line-height: 1.6;
  max-width: 32rem;
  margin-bottom: 2rem;
}

.hero-image {
  position: relative;
  height: 40vh;
  transition: transform 0.3s ease-in-out;
}

.hero-image:hover {
  transform: scale(1.05);
}

.hero-image img {
  height: 100%;
  object-fit: contain;
  filter: drop-shadow(0 10px 30px rgba(0, 0, 0, 0.1));
}

/* Contact Form Section */
.contact-section {
  padding: 4rem 0;
  background-color: var(--white);
}

.contact-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 3rem;
}

.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.contact-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border-radius: 0.75rem;
  transition: all 0.2s ease-in-out;
}

.contact-card:hover {
  background-color: rgba(45, 140, 136, 0.05);
}

.contact-icon {
  width: 3rem;
  height: 3rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: rgba(45, 140, 136, 0.1);
  color: var(--primary);
  transition: all 0.2s ease-in-out;
}

.contact-card:hover .contact-icon {
  background-color: var(--primary);
  color: var(--white);
}

.contact-form {
  background-color: var(--white);
  border-radius: 1rem;
  padding: 2rem;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.form-group {
  margin-bottom: 1.5rem;
}

.form-label {
  display: block;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--text);
  margin-bottom: 0.5rem;
}

.form-input {
  width: 100%;
  padding: 0.75rem 1rem;
  border: 1px solid #E5E7EB;
  border-radius: 0.5rem;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
}

.form-input:focus {
  outline: none;
  border-color: var(--primary);
  box-shadow: 0 0 0 3px rgba(45, 140, 136, 0.1);
}

.form-input.error {
  border-color: #EF4444;
}

.error-message {
  color: #EF4444;
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

/* Social Links */
.social-links {
  display: flex;
  gap: 1rem;
}

.social-link {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: rgba(45, 140, 136, 0.1);
  color: var(--primary);
  transition: all 0.2s ease-in-out;
}

.social-link:hover {
  background-color: var(--primary);
  color: var(--white);
  transform: translateY(-2px);
}

/* Success Message */
.success-message {
  text-align: center;
  padding: 2rem;
}

.success-icon {
  width: 4rem;
  height: 4rem;
  margin: 0 auto 1rem;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 9999px;
  background-color: rgba(45, 140, 136, 0.1);
  color: var(--primary);
}

/* Responsive Design */
@media (min-width: 640px) {
  .container {
    padding: 0 1.5rem;
  }

  .hero-image {
    height: 50vh;
  }
}

@media (min-width: 768px) {
  .container {
    padding: 0 2rem;
  }

  .hero-section {
    padding: 8rem 0;
  }

  .hero-image {
    height: 60vh;
  }

  .contact-grid {
    grid-template-columns: 1fr 1fr;
    gap: 4rem;
  }

  .contact-form {
    padding: 2.5rem;
  }
}

@media (min-width: 1024px) {
  .container {
    padding: 0 2.5rem;
  }

  .hero-section {
    padding: 10rem 0;
  }

  .hero-image {
    height: 70vh;
  }
}

/* Touch Device Optimizations */
@media (hover: none) {
  .btn:hover,
  .contact-card:hover,
  .social-link:hover {
    transform: none;
  }

  .hero-image:hover {
    transform: none;
  }
}

/* Safari Mobile Fixes */
@supports (-webkit-touch-callout: none) {
  .hero-image {
    height: 40vh;
  }

  @media (min-width: 640px) {
    .hero-image {
      height: 50vh;
    }
  }

  @media (min-width: 768px) {
    .hero-image {
      height: 60vh;
    }
  }

  @media (min-width: 1024px) {
    .hero-image {
      height: 70vh;
    }
  }
}

/* Chrome Mobile Fixes */
@media screen and (-webkit-min-device-pixel-ratio: 0) {
  .hero-image {
    height: 40vh;
  }

  @media (min-width: 640px) {
    .hero-image {
      height: 50vh;
    }
  }

  @media (min-width: 768px) {
    .hero-image {
      height: 60vh;
    }
  }

  @media (min-width: 1024px) {
    .hero-image {
      height: 70vh;
    }
  }
} 