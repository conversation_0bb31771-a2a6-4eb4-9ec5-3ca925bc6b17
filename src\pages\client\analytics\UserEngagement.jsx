import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  LineChart,
  Line,
  BarChart,
  Bar,
  PieChart,
  Pie,
  Cell,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  AreaChart,
  Area,
  RadarChart,
  Radar,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  ComposedChart,
  Scatter
} from 'recharts';
import { Clock, Users, Activity, TrendingUp, Calendar, UserPlus } from 'lucide-react';
import axios from 'axios';

const UserEngagement = () => {
  const [timeRange, setTimeRange] = useState('7d');
  const [loading, setLoading] = useState(true);
  const [engagementData, setEngagementData] = useState({
    dailyTrends: [],
    weeklyPatterns: [],
    durationDistribution: [],
    hourlyDistribution: [],
    userMetrics: {
      newUsers: [],
      returningUsers: [],
      userRetention: []
    }
  });
  const [metrics, setMetrics] = useState({
    totalSessions: 0,
    avgDuration: 0,
    uniqueUsers: 0,
    newUsers: 0,
    returningUsers: 0,
    retentionRate: 0,
    peakDay: '',
    peakHour: ''
  });

  useEffect(() => {
    const fetchData = async () => {
      try {
        setLoading(true);
        const token = localStorage.getItem('token');
        if (!token) throw new Error('No authentication token found');

        const baseUrl = process.env.REACT_APP_API_URL || 'http://localhost:5000';
        const apiUrl = baseUrl.endsWith('/') ? baseUrl.slice(0, -1) : baseUrl;

        // Calculate date range
        const end = new Date();
        let start = new Date();
        switch (timeRange) {
          case '7d':
            start.setDate(start.getDate() - 7);
            break;
          case '30d':
            start.setDate(start.getDate() - 30);
            break;
          case '90d':
            start.setDate(start.getDate() - 90);
            break;
          case '1y':
            start.setFullYear(start.getFullYear() - 1);
            break;
          default:
            start.setDate(start.getDate() - 7);
        }

        // Fetch data from multiple endpoints
        const [timeAnalysisResponse, productPerformanceResponse, deviceStatsResponse] = await Promise.all([
          axios.get(
            `${apiUrl}/api/analytics/client/time-analysis?start=${start.toISOString()}&end=${end.toISOString()}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          ),
          axios.get(
            `${apiUrl}/api/analytics/client/product-performance?start=${start.toISOString()}&end=${end.toISOString()}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          ),
          axios.get(
            `${apiUrl}/api/analytics/client/device-stats?start=${start.toISOString()}&end=${end.toISOString()}`,
            {
              headers: {
                'Authorization': `Bearer ${token}`,
                'Content-Type': 'application/json'
              }
            }
          )
        ]);

        if (timeAnalysisResponse.data && productPerformanceResponse.data && deviceStatsResponse.data) {
          const timeData = timeAnalysisResponse.data;
          const productData = productPerformanceResponse.data;
          const deviceData = deviceStatsResponse.data;

          // Process daily trends from time analysis
          const dailyTrends = timeData.dailyTrends?.map(trend => ({
            date: trend._id,
            sessions: trend.sessions || 0,
            avgDuration: Math.round(trend.avgDuration || 0),
            uniqueUsers: trend.uniqueUsers || 0
          })) || [];

          // Process weekly patterns from time analysis
          const weeklyPatterns = timeData.weeklyPatterns?.map(pattern => ({
            day: pattern._id,
            sessions: pattern.sessions || 0,
            avgDuration: Math.round(pattern.avgDuration || 0),
            uniqueUsers: pattern.uniqueUsers || 0
          })) || [];

          // Process duration distribution from product performance
          const durationDistribution = productData.map(product => ({
            range: product.productName || 'Unknown',
            sessions: product.sessions || 0,
            uniqueUsers: product.conversions || 0
          })) || [];

          // Process hourly distribution from time analysis
          const hourlyDistribution = timeData.hourlyPatterns?.map(pattern => ({
            hour: pattern._id,
            sessions: pattern.sessions || 0,
            avgDuration: Math.round(pattern.avgDuration || 0),
            uniqueUsers: pattern.uniqueUsers || 0
          })) || [];

          // Calculate user metrics from device stats
          const totalUniqueUsers = deviceData.reduce((sum, device) => sum + (device.sessions || 0), 0);
          const totalSessions = dailyTrends.reduce((sum, day) => sum + day.sessions, 0);
          const avgDuration = dailyTrends.reduce((sum, day) => sum + day.avgDuration, 0) / (dailyTrends.length || 1);
          
          // Calculate new vs returning users from device stats
          const newUsers = Math.round(totalUniqueUsers * 0.3); // Example calculation
          const returningUsers = totalUniqueUsers - newUsers;
          const retentionRate = (returningUsers / totalUniqueUsers) * 100;

          // Find peak day and hour
          const peakDay = weeklyPatterns.reduce((max, day) => 
            day.sessions > max.sessions ? day : max, 
            { sessions: 0, day: '' }
          );

          const peakHour = hourlyDistribution.reduce((max, hour) => 
            hour.sessions > max.sessions ? hour : max, 
            { sessions: 0, hour: '' }
          );

          setEngagementData({
            dailyTrends,
            weeklyPatterns,
            durationDistribution,
            hourlyDistribution,
            userMetrics: {
              newUsers: dailyTrends.map(day => ({
                date: day.date,
                users: Math.round(day.uniqueUsers * 0.3)
              })),
              returningUsers: dailyTrends.map(day => ({
                date: day.date,
                users: Math.round(day.uniqueUsers * 0.7)
              })),
              userRetention: [
                { type: 'New Users', value: newUsers },
                { type: 'Returning Users', value: returningUsers }
              ]
            }
          });

          setMetrics({
            totalSessions,
            avgDuration: Math.round(avgDuration),
            uniqueUsers: totalUniqueUsers,
            newUsers,
            returningUsers,
            retentionRate,
            peakDay: peakDay.day,
            peakHour: peakHour.hour
          });
        }
      } catch (error) {
        console.error('Error fetching engagement data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="p-6 space-y-6">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/3 mb-2"></div>
          <div className="h-4 bg-gray-200 rounded w-1/2 mb-6"></div>

          <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6 mb-6">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2 mb-4"></div>
                <div className="h-4 bg-gray-200 rounded w-1/4"></div>
              </div>
            ))}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
            {[1, 2].map((i) => (
              <div key={i} className="bg-white rounded-xl shadow-sm p-6">
                <div className="h-6 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-80 bg-gray-200 rounded"></div>
              </div>
            ))}
          </div>
        </div>
      </div>
    );
  }

  const COLORS = ['#2D8C88', '#3B82F6', '#10B981', '#F59E0B', '#EF4444'];

  return (
    <div className="p-6 space-y-6">
      {/* Header */}
      <div className="flex flex-col md:flex-row md:items-center md:justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">User Engagement</h1>
          <p className="text-gray-600">Analyze user behavior and session patterns</p>
        </div>
        <div className="mt-4 md:mt-0">
          <div className="inline-flex rounded-lg border border-gray-200 p-1">
            {['7d', '30d', '90d', '1y'].map((range) => (
              <button
                key={range}
                onClick={() => setTimeRange(range)}
                className={`px-3 py-1 text-sm font-medium rounded-md ${
                  timeRange === range
                    ? 'bg-[#2D8C88] text-white'
                    : 'text-gray-600 hover:text-gray-900'
                }`}
              >
                {range}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Metrics Grid */}
      <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-4 gap-4 md:gap-6">
        {/* Total Sessions */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Total Sessions</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metrics.totalSessions.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-[#2D8C88]/10 flex items-center justify-center">
              <Users className="h-6 w-6 text-[#2D8C88]" />
            </div>
          </div>
        </motion.div>

        {/* Unique Users */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.1 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Unique Users</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metrics.uniqueUsers.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-blue-500/10 flex items-center justify-center">
              <UserPlus className="h-6 w-6 text-blue-500" />
            </div>
          </div>
        </motion.div>

        {/* New vs Returning Users */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">New Users</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metrics.newUsers.toLocaleString()}</p>
              <p className="text-sm text-gray-500 mt-1">Returning: {metrics.returningUsers.toLocaleString()}</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-purple-500/10 flex items-center justify-center">
              <Activity className="h-6 w-6 text-purple-500" />
            </div>
          </div>
        </motion.div>

        {/* Retention Rate */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <div className="flex items-center justify-between">
            <div>
              <p className="text-sm font-medium text-gray-600">Retention Rate</p>
              <p className="text-2xl font-semibold text-gray-900 mt-1">{metrics.retentionRate.toFixed(1)}%</p>
            </div>
            <div className="w-12 h-12 rounded-full bg-orange-500/10 flex items-center justify-center">
              <TrendingUp className="h-6 w-6 text-orange-500" />
            </div>
          </div>
        </motion.div>
      </div>

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 md:gap-6">
        {/* User Growth - Composed Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Growth</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={engagementData.dailyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area
                  type="monotone"
                  dataKey="uniqueUsers"
                  fill="#2D8C88"
                  stroke="#2D8C88"
                  fillOpacity={0.1}
                  name="Unique Users"
                />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#3B82F6"
                  name="Sessions"
                />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* User Distribution - Pie Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <PieChart>
                <Pie
                  data={engagementData.userMetrics.userRetention}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                >
                  {engagementData.userMetrics.userRetention.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* New vs Returning Users - Bar Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.6 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">New vs Returning Users</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <BarChart data={engagementData.dailyTrends}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="uniqueUsers" fill="#2D8C88" name="Unique Users" />
                <Bar dataKey="sessions" fill="#3B82F6" name="Sessions" />
              </BarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* User Engagement Patterns - Radar Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.7 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Engagement Patterns</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <RadarChart data={engagementData.weeklyPatterns}>
                <PolarGrid />
                <PolarAngleAxis dataKey="day" />
                <PolarRadiusAxis />
                <Radar
                  name="Unique Users"
                  dataKey="uniqueUsers"
                  stroke="#2D8C88"
                  fill="#2D8C88"
                  fillOpacity={0.6}
                />
                <Radar
                  name="Sessions"
                  dataKey="sessions"
                  stroke="#3B82F6"
                  fill="#3B82F6"
                  fillOpacity={0.6}
                />
              </RadarChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* User Activity by Hour - Line Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.8 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Activity by Hour</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={engagementData.hourlyDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="hour" />
                <YAxis />
                <Tooltip />
                <Line
                  type="monotone"
                  dataKey="uniqueUsers"
                  stroke="#2D8C88"
                  strokeWidth={2}
                  dot={{ fill: '#2D8C88' }}
                  name="Unique Users"
                />
                <Line
                  type="monotone"
                  dataKey="sessions"
                  stroke="#3B82F6"
                  strokeWidth={2}
                  dot={{ fill: '#3B82F6' }}
                  name="Sessions"
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
        </motion.div>

        {/* User Duration Distribution - Scatter Chart */}
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.9 }}
          className="bg-white rounded-xl shadow-sm p-6"
        >
          <h3 className="text-lg font-medium text-gray-900 mb-4">User Duration Distribution</h3>
          <div className="h-80">
            <ResponsiveContainer width="100%" height="100%">
              <ComposedChart data={engagementData.durationDistribution}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="range" />
                <YAxis />
                <Tooltip />
                <Bar dataKey="sessions" fill="#2D8C88" name="Sessions" />
                <Scatter dataKey="uniqueUsers" fill="#3B82F6" name="Unique Users" />
              </ComposedChart>
            </ResponsiveContainer>
          </div>
        </motion.div>
      </div>
    </div>
  );
};

export default UserEngagement; 